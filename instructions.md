# Contributing to CardCastle

## Code Organization

- `src/models/` - Data models for different card games and app features
- `src/views/` - React components and pages
- `src/api/` - API client code
- `src/lib/` - Utility libraries
- `src/helpers/` - Helper functions
- `assets/scss/` - Styling organized by component type

## Naming Conventions

- Use PascalCase for React components, interfaces (prefixed with 'I'), and class names
- Use camelCase for variables, functions, and methods
- Use UPPER_SNAKE_CASE for constants and enums
- Prefix boolean variables with 'is', 'has', or 'should'

## Type Safety

- Always use TypeScript interfaces or types for props and state
- Prefer interfaces for object shapes and types for unions/intersections
- Use `Record<T>` for immutable data structures

## Component Guidelines

- Keep components focused on a single responsibility
- Extract reusable logic into custom hooks
- Use functional components with hooks instead of class components

## Testing

- Write tests for all new features
- We use vitest in the project and we have 1 config in the `vite.config.ts` file
- We run test coverage using `yarn coverage` and the `vitest-coverage.config.ts` file
- When writing unit test, colocate them next to the file being tested
- Write and group unit test so that they read like a story when you run the test file
- When running the test file, run only the tests you added first and then run the whole test file to make sure that the tests do not impact each other.
- `waitFor` already has a default timeout of `1_000ms` so no need to add it as an option
- Prefer using `await findBy` over `await waitFor`
- Do not use `vi.clearAllMocks()` in `beforeEach` as the config is already set to `clearMocks: true`
- Prefer finding by role over finding by text or by using class names
- Prefer using `fireEvent` over `userEvent`
- Do not test `event.preventDefault()`
- Prefer getting elements by their role rather than using class names
- Avoid testing similar things multiple times
- If you are testing components to have certain attributes, you should assume that it is already in the document
- Run `yarn test` to ensure all tests pass
- You do not need to call `vi.clearAllMocks();` because there is a config which ensures mocks are cleared between tests.
- Never make code changes to the component

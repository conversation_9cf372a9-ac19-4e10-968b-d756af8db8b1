import plusIcon from '@iconify/icons-ic/baseline-plus';
import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { Grouping } from '../../../models/Grouping';
import { SelectCell } from '../Table';

const mockOnClick = vi.fn();
const mockOnChange = vi.fn();
const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
  value: 'option1',
  grouping: Grouping.NONE,
  onChange: mockOnChange,
};

function renderSelectCell(props: Partial<ComponentProps<typeof SelectCell>> = {}) {
  return render(
    <SelectCell {...DEFAULT_PROPS} {...props}>
      <option value="option1">Option 1</option>
      <option value="option2">Option 2</option>
      <option value="option3">Option 3</option>
    </SelectCell>,
  );
}

describe('SelectCell', () => {
  it('renders select element with correct value', () => {
    renderSelectCell();
    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();
    expect(select).toHaveValue(DEFAULT_PROPS.value);
  });

  it('renders all option children', () => {
    renderSelectCell();
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('Option 3')).toBeInTheDocument();
  });

  it('renders icon when icon prop is provided', () => {
    const { container } = renderSelectCell({ icon: plusIcon });
    const iconElement = container.querySelector('svg');
    expect(iconElement).toBeInTheDocument();
  });
  it('calls onChange when select value changes', () => {
    renderSelectCell();
    const selectElement = screen.getByRole('combobox');
    fireEvent.change(selectElement, { target: { value: 'option2' } });
    expect(mockOnChange).toHaveBeenCalledTimes(1);
  });
});

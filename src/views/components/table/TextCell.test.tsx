import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { TextCell } from '../Table';

const mockOnClick = vi.fn();

const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
  text: 'Sample Text',
};
function renderTextCell(props: Partial<ComponentProps<typeof TextCell>> = {}) {
  return render(<TextCell {...DEFAULT_PROPS} {...props}></TextCell>);
}

describe('TextCell', () => {
  it('renders text content', () => {
    renderTextCell();
    expect(screen.getByText(DEFAULT_PROPS.text)).toBeInTheDocument();
  });
  it('triggers onClick when clicked', () => {
    renderTextCell();
    fireEvent.click(screen.getByText(DEFAULT_PROPS.text));
    expect(mockOnClick).toBeCalled();
  });
});

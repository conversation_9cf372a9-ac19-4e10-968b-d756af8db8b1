import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { ParentCell } from '../Table';

const mockOnClick = vi.fn();
const DEFAULT_CELL_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
};

const DEFAULT_PARENT_CELL_PROPS = DEFAULT_CELL_PROPS;
function renderParentCell(props: Partial<ComponentProps<typeof ParentCell>> = {}) {
  return render(
    <ParentCell {...DEFAULT_PARENT_CELL_PROPS} {...props}>
      {props.children}
    </ParentCell>,
  );
}
describe('ParentCell', () => {
  it('renders an empty cell when no children are provided', () => {
    const { container } = renderParentCell();
    expect((container.querySelector('.table__cell') as HTMLElement).firstChild).toBeNull();
  });
  it('renders children content when provided', () => {
    renderParentCell({
      children: <span>Test Content</span>,
    });
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
  it('triggers onClick when clicked', () => {
    const { container } = renderParentCell();
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(mockOnClick).toHaveBeenCalled();
  });
});

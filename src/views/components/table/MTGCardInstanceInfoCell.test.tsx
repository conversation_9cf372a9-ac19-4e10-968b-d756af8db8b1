import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeCardInstanceWithOptions } from '../../../../tests/fake/FakeCardData';
import { CellBorders } from '../../../models/CellBorder';
import { Grouping } from '../../../models/Grouping';
import { ScanMetadata } from '../../../models/ScanMetadata';
import { MTGCardInstanceInfoCell } from '../Table';

const mockOnClick = vi.fn();

const DEFAULT_PROPS = {
  cellStyle: {
    dark: false,
    onClick: mockOnClick,
    cellBorders: new CellBorders(),
  },
  cardInstance: create(FakeCardInstanceWithOptions, {
    id: 123,
    scanMetadata: new ScanMetadata({ confidence: 0.8 }),
  }),
  grouping: Grouping.NONE,
  displayConfidence: false,
};
const renderMTGCardInstanceInfoCell = (props: Partial<ComponentProps<typeof MTGCardInstanceInfoCell>> = {}) => {
  return render(<MTGCardInstanceInfoCell {...DEFAULT_PROPS} {...props} />);
};

describe('MTGCardInstanceInfoCell', () => {
  describe('when rendered', () => {
    it('renders card name', () => {
      const cardName = DEFAULT_PROPS.cardInstance.get('cardName');
      renderMTGCardInstanceInfoCell();
      expect(screen.getByText(cardName)).toBeInTheDocument();
    });
    it('renders SetSymbol with size SM', () => {
      renderMTGCardInstanceInfoCell();
      expect(screen.getByRole('img').parentElement?.classList).toContain('set-symbol__sm');
    });
    it('triggers onClick when cell is clicked', () => {
      const { container } = renderMTGCardInstanceInfoCell();
      const cellElement = container.querySelector('.table__cell') as HTMLElement;
      cellElement.click();
      expect(mockOnClick).toHaveBeenCalled();
    });
  });
  describe('with CardInfoCell', () => {
    it('shows confidence pill when displayConfidence is true and confidence exists', () => {
      const { container } = renderMTGCardInstanceInfoCell({
        displayConfidence: true,
      });
      expect(container.querySelector('.card-cell-confidence')).toBeInTheDocument();
    });

    it('hides confidence pill when displayConfidence is false', () => {
      const { container } = renderMTGCardInstanceInfoCell({
        displayConfidence: false,
      });
      expect(container.querySelector('.card-cell-confidence')).not.toBeInTheDocument();
    });
    it('handles undefined confidence correctly', () => {
      const { container } = renderMTGCardInstanceInfoCell({
        cardInstance: create(FakeCardInstanceWithOptions, { scanMetadata: undefined }),
        displayConfidence: true,
      });
      expect(container.querySelector('.card-cell-confidence')).not.toBeInTheDocument();
    });
  });
});

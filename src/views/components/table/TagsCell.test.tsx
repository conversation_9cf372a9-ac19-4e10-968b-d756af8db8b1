import { render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import React, { ComponentProps } from 'react';
import { describe, expect, it } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeCardInstanceWithOptions, FakeTagWithOptions } from '../../../../tests/fake/FakeCardData';
import { CellBorders } from '../../../models/CellBorder';
import { Tag } from '../../../models/Tags';
import { TagsCell } from '../Table';

const defaultCardId = { id: 123 };
const DEFAULT_PROPS = {
  dark: false,
  cellBorders: new CellBorders(),
  cardInstance: create(FakeCardInstanceWithOptions, defaultCardId),
  cardInstanceTags: Immutable.Map<number, Immutable.List<Tag>>(),
};

function renderTagsCell(props: Partial<ComponentProps<typeof TagsCell>> = {}) {
  return render(<TagsCell {...DEFAULT_PROPS} {...props} />);
}
describe('TagsCell', () => {
  it('renders with basic props', () => {
    const { container } = renderTagsCell();
    const cellElement = container.querySelector('.table__cell');
    expect(cellElement).toBeInTheDocument();
  });

  it('displays "Loading..." when cardInstanceTags is undefined for the card', () => {
    const cardInstanceTags = Immutable.Map<number, Immutable.List<Tag>>();
    renderTagsCell({ cardInstanceTags });
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('displays "-" when cardInstanceTags exists but is empty', () => {
    const cardInstance = create(FakeCardInstanceWithOptions, defaultCardId);

    const cardInstanceTags = Immutable.Map<number, Immutable.List<Tag>>([[defaultCardId.id, Immutable.List<Tag>()]]);
    renderTagsCell({ cardInstance, cardInstanceTags });
    expect(screen.getByText('-')).toBeInTheDocument();
  });

  it('displays tags when cardInstanceTags has tags for the card', () => {
    const cardInstance = create(FakeCardInstanceWithOptions, defaultCardId);
    const tag1 = create(FakeTagWithOptions, { name: 'vintage' });
    const tag2 = create(FakeTagWithOptions, { name: 'commander' });
    const cardInstanceTags = Immutable.Map<number, Immutable.List<Tag>>([
      [defaultCardId.id, Immutable.List([tag1, tag2])],
    ]);
    renderTagsCell({ cardInstance, cardInstanceTags });

    expect(screen.getByText('#vintage')).toBeInTheDocument();
    expect(screen.getByText('#commander')).toBeInTheDocument();
  });
});

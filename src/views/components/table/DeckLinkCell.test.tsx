import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeCardInstanceWithOptions, FakeDeck } from '../../../../tests/fake/FakeCardData';
import { CellBorders } from '../../../models/CellBorder';
import { LinkedResources } from '../../../models/LinkedResources';
import { DeckLinkCell } from '../Table';

const defaultCardId = { id: 123 };
const mockDeck = create(FakeDeck, defaultCardId);
const DEFAULT_PROPS = {
  dark: false,
  cellBorders: new CellBorders(),
  // Link deck to the same deck for default props
  deck: mockDeck,
  cardInstance: create(FakeCardInstanceWithOptions, {
    ...defaultCardId,
    linkedResources: new LinkedResources({
      deck: mockDeck,
    }),
  }),
  isConfirming: false,
  onRequestConfirmation: vi.fn(),
  onLink: vi.fn(),
};

function renderDeckLinkCell(props: Partial<ComponentProps<typeof DeckLinkCell>> = {}) {
  return render(<DeckLinkCell {...DEFAULT_PROPS} {...props} />);
}

describe('DeckLinkCell', () => {
  it('renders with basic props', () => {
    const { container } = renderDeckLinkCell();
    const cellElement = container.querySelector('.table__cell');
    expect(cellElement).toBeInTheDocument();
    expect(cellElement).toHaveStyle({ cursor: 'pointer' });
  });

  it('displays link icon when not confirming', () => {
    const { container } = renderDeckLinkCell({ isConfirming: false });
    const icon = container.querySelector('svg');
    expect(icon).toBeInTheDocument();
    expect(container.querySelector('.hover-hint-container')).not.toBeInTheDocument();
  });

  it('displays confirmation UI when confirming', () => {
    const { container } = renderDeckLinkCell({ isConfirming: true });
    expect(container.querySelector('.hover-hint-container')).toBeInTheDocument();
    expect(container.querySelector('.hover-hint')).toBeInTheDocument();
    expect(container.querySelector('.table-deletion-hint')).toBeInTheDocument();
    expect(screen.getByText('Are you sure?')).toBeInTheDocument();
  });
  test('calls onLink when clicked and confirming prop is true', () => {
    const { container } = renderDeckLinkCell({ isConfirming: true });
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(DEFAULT_PROPS.onLink).toHaveBeenCalledTimes(1);
    expect(DEFAULT_PROPS.onRequestConfirmation).not.toHaveBeenCalled();
  });
  test('calls onLink when clicked and no linked deck', () => {
    const { container } = renderDeckLinkCell({
      cardInstance: create(FakeCardInstanceWithOptions, defaultCardId),
    });
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(DEFAULT_PROPS.onLink).toHaveBeenCalledTimes(1);
    expect(DEFAULT_PROPS.onRequestConfirmation).not.toHaveBeenCalled();
  });
  test('calls onRequestConfirmation when clicked with linked deck to different deck and not confirming', () => {
    const { container } = renderDeckLinkCell({
      // create different deck with id thats not linked to card
      deck: create(FakeDeck, { id: 124 }),
      isConfirming: false,
    });
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(DEFAULT_PROPS.onRequestConfirmation).toHaveBeenCalledTimes(1);
    expect(DEFAULT_PROPS.onLink).not.toHaveBeenCalled();
  });
});

import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { AmountCell } from '../Table';

const mockOnClick = vi.fn();
const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
  amount: 12000,
};

function renderAmountCell(props: Partial<ComponentProps<typeof AmountCell>> = {}) {
  return render(<AmountCell {...DEFAULT_PROPS} {...props} />);
}

describe('AmountCell', () => {
  it('renders with value', () => {
    const { container } = renderAmountCell();
    expect(container.querySelector('.amount-container')).toBeInTheDocument();
    expect(container.querySelector('.amount')).toBeInTheDocument();
    expect(screen.getByText(DEFAULT_PROPS.amount)).toBeInTheDocument();
  });

  it('triggers onClick when clicked', () => {
    const { container } = renderAmountCell();
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(mockOnClick).toHaveBeenCalled();
  });
});

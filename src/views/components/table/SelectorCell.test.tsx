import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { SelectorCell } from '../Table';

const mockOnClick = vi.fn();
const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
};

function renderSelectorCell(props: Partial<ComponentProps<typeof SelectorCell>> = {}) {
  return render(
    <SelectorCell {...DEFAULT_PROPS} {...props}>
      {props.children}
    </SelectorCell>,
  );
}

describe('SelectorCell', () => {
  it('renders an empty cell when no children are provided', () => {
    const { container } = renderSelectorCell();
    expect((container.querySelector('.table__cell') as HTMLElement).firstChild).toBeNull();
  });
  it('renders children content when provided', () => {
    renderSelectorCell({
      children: <div>Test Selector Content</div>,
    });
    expect(screen.getByText('Test Selector Content')).toBeInTheDocument();
  });
});

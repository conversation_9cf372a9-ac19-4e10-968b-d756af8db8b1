import { faker } from '@faker-js/faker';
import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Card } from '../../../models/Cards';
import { CellBorders } from '../../../models/CellBorder';
import { Grouping } from '../../../models/Grouping';
import { Rarity } from '../../../models/Rarity';
import { MTGCardInfoCell } from '../Table';

const mockOnClick = vi.fn();
const DEFAULT_PROPS = {
  cellStyle: {
    dark: false,
    onClick: mockOnClick,
    cellBorders: new CellBorders(),
  },
  card: new Card({
    name: faker.commerce.productName(),
    setName: faker.commerce.department(),
    setCode: faker.string.alphanumeric(3).toUpperCase(),
    collectorNumber: faker.string.numeric(3),
    rarity: Rarity.RARE,
    ownedCount: faker.number.int({ min: 0, max: 4 }),
  }),
  grouping: Grouping.NONE,
};
const renderMTGInfoCell = (props: Partial<ComponentProps<typeof MTGCardInfoCell>> = {}) => {
  return render(<MTGCardInfoCell {...DEFAULT_PROPS} {...props} />);
};

describe('MTGCardInfoCell', () => {
  it('renders card name', () => {
    const cardName = DEFAULT_PROPS.card.get('name');
    renderMTGInfoCell();
    expect(screen.getByText(cardName)).toBeInTheDocument();
  });
  it('triggers onClick when cell is clicked', () => {
    const { container } = renderMTGInfoCell();
    const cellElement = container.querySelector('.table__cell') as HTMLElement;
    cellElement.click();
    expect(mockOnClick).toHaveBeenCalled();
  });
  it('renders SetSymbol with size SM', () => {
    renderMTGInfoCell();
    expect(screen.getByRole('img').parentElement?.classList).toContain('set-symbol__sm');
  });
});

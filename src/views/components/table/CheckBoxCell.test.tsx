import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { CheckBoxCell } from '../Table';

const mockOnChange = vi.fn();
const DEFAULT_PROPS = {
  dark: false,
  cellBorders: new CellBorders(),
  checked: false,
  onChange: mockOnChange,
  disabled: false,
  multiple: false,
};

const renderCheckboxCell = (props: Partial<ComponentProps<typeof CheckBoxCell>> = {}) => {
  return render(<CheckBoxCell {...DEFAULT_PROPS} {...props} />);
};

describe('CheckBoxCell', () => {
  it('renders checkbox input', () => {
    const { container } = renderCheckboxCell();
    const innerDiv = container.querySelector('.checkbox-override');
    expect(innerDiv).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
  });
  it('handles checked state correctly', () => {
    renderCheckboxCell({ checked: true });
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox.parentElement).toHaveClass('is-checked');
  });
  test('handles unchecked state correctly', () => {
    renderCheckboxCell({ checked: false });
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox.parentElement).not.toHaveClass('is-checked');
  });
  test('triggers onChange when clicked', () => {
    renderCheckboxCell();
    fireEvent.click(screen.getByRole('checkbox'));
    expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(true);
  });
  it('disables checkbox when disabled=true', () => {
    renderCheckboxCell({ disabled: true });
    expect(screen.getByRole('checkbox')).toBeDisabled();
  });
});

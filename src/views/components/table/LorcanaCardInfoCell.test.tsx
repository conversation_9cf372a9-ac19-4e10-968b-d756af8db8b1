import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakePokeCardInstanceWithOptions, FakeYugiCardInstanceWithOptions } from '../../../../tests/fake/FakeCardData';
import { CellBorders } from '../../../models/CellBorder';
import { Grouping } from '../../../models/Grouping';
import { ScanMetadata } from '../../../models/ScanMetadata';
import { LorcanaCardInfoCell } from '../Table';

const mockOnClick = vi.fn();
const DEFAULT_PROPS = {
  cellStyle: {
    dark: false,
    onClick: mockOnClick,
    cellBorders: new CellBorders(),
  },
  cardInstance: create(FakeYugiCardInstanceWithOptions, {
    scanMetadata: new ScanMetadata({ confidence: 0.8 }),
  }),
  grouping: Grouping.NONE,
  displayConfidence: false,
};
const renderLorcanaCardInfoCell = (props: Partial<ComponentProps<typeof LorcanaCardInfoCell>> = {}) => {
  return render(<LorcanaCardInfoCell {...DEFAULT_PROPS} {...props} />);
};

describe('LorcanaCardInfoCell', () => {
  describe('when rendered', () => {
    it('renders card name', () => {
      const cardName = DEFAULT_PROPS.cardInstance.get('card').get('name');
      renderLorcanaCardInfoCell();
      expect(screen.getByText(cardName)).toBeInTheDocument();
    });
    it('renders LorcanaSetSymbol with size SM', () => {
      const { container } = renderLorcanaCardInfoCell();
      expect(container.querySelector('.set-symbol__sm')).toBeInTheDocument();
    });
    it('triggers onClick when cell is clicked', () => {
      const { container } = renderLorcanaCardInfoCell();
      const cellElement = container.querySelector('.table__cell') as HTMLElement;
      cellElement.click();
      expect(mockOnClick).toHaveBeenCalled();
    });
  });

  describe('with confidence display', () => {
    it('shows confidence pill when displayConfidence is true and confidence exists', () => {
      const { container } = renderLorcanaCardInfoCell({
        displayConfidence: true,
      });
      expect(container.querySelector('.card-cell-confidence')).toBeInTheDocument();
    });

    it('hides confidence pill when displayConfidence is false', () => {
      const { container } = renderLorcanaCardInfoCell({
        displayConfidence: false,
      });
      expect(container.querySelector('.card-cell-confidence')).not.toBeInTheDocument();
    });
    it('handles undefined confidence correctly', () => {
      const { container } = renderLorcanaCardInfoCell({
        cardInstance: create(FakePokeCardInstanceWithOptions, { scanMetadata: undefined }),
        displayConfidence: true,
      });
      expect(container.querySelector('.card-cell-confidence')).not.toBeInTheDocument();
    });
  });
});

import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { CheckCell } from '../Table';

const mockOnClick = vi.fn();

const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
  checked: false,
};
function renderCheckCell(props: Partial<ComponentProps<typeof CheckCell>> = {}) {
  return render(<CheckCell {...DEFAULT_PROPS} {...props}></CheckCell>);
}

describe('CheckCell', () => {
  it('renders check icon when checked', () => {
    const { container } = renderCheckCell({ checked: true });
    expect(container.querySelector('svg')).toBeInTheDocument();
  });
  it('renders dash when unchecked', () => {
    renderCheckCell();
    expect(screen.getByText('-')).toBeInTheDocument();
  });
  it('triggers onClick when clicked', () => {
    const { container } = renderCheckCell();
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(mockOnClick).toHaveBeenCalled();
  });
});

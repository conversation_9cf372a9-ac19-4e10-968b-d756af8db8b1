import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { Rarity } from '../../../models/Rarity';
import { SetIconCell } from '../Table';
const mockOnClick = vi.fn();

const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
  setName: 'Test Set',
  setCode: 'TST',
  hoverText: true,
  rarity: Rarity.COMMON,
  collectorNumber: '001',
};

function renderSetIconCell(props: Partial<ComponentProps<typeof SetIconCell>> = {}) {
  return render(<SetIconCell {...DEFAULT_PROPS} {...props} />);
}
describe('SetIconCell', () => {
  it('renders correctly with basic props', () => {
    const { container } = renderSetIconCell();
    const cellElement = container.querySelector('.table__cell');
    expect(cellElement).toBeInTheDocument();
  });
  it('renders SetSymbol with default size XS', () => {
    renderSetIconCell();
    expect(screen.getByRole('img').parentElement?.classList).toContain('set-symbol__xs');
  });
  it('triggers onClick when clicked', () => {
    const { container } = renderSetIconCell();
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(DEFAULT_PROPS.onClick).toHaveBeenCalled();
  });
});

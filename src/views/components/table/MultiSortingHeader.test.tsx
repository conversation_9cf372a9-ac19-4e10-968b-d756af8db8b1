import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { MultiSortingHeader } from '../Table';

const DEFAULT_PROPS = {
  title: 'Multi Sort Header',
  sorting: 'name',
  asc: ['name', 'price'],
  desc: ['date'],
  cellBorders: new CellBorders(),
  collectionCell: false,
  large: false,
};

function renderMultiSortingHeader(props: Partial<ComponentProps<typeof MultiSortingHeader>> = {}) {
  return render(<MultiSortingHeader {...DEFAULT_PROPS} {...props} />);
}

describe('MultiSortingHeader', () => {
  describe('when rendered', () => {
    it('renders header title', () => {
      renderMultiSortingHeader();
      expect(screen.getByText(DEFAULT_PROPS.title)).toBeInTheDocument();
    });
    it('shows icon when when sorting prop matches asc prop or desc prop', () => {
      const propsComponent = {
        ...DEFAULT_PROPS,
        sorting: 'name',
        asc: ['name', 'price'],
        desc: ['date'],
      };
      // Test when sorting matches asc prop ('name' is in asc array)
      const { container, rerender } = renderMultiSortingHeader(propsComponent);
      expect(container.querySelector('svg')).toBeInTheDocument();

      // Test when sorting matches desc prop ('date' is in desc array)
      rerender(<MultiSortingHeader {...propsComponent} sorting="date" />);
      expect(container.querySelector('svg')).toBeInTheDocument();

      // Test when sorting matches neither asc prop nor desc prop
      rerender(<MultiSortingHeader {...propsComponent} sorting="unknown" />);
      expect(container.querySelector('svg')).toBeNull();
    });
    test('using empty asc and desc arrays', () => {
      const { container } = renderMultiSortingHeader({
        sorting: 'name',
        asc: [],
        desc: [],
      });
      expect(screen.getByText(DEFAULT_PROPS.title)).toBeInTheDocument();
      expect(container.querySelector('svg')).toBeNull();
    });
  });
});

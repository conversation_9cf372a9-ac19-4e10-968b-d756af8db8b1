import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakePokeCardInstanceWithOptions } from '../../../../tests/fake/FakeCardData';
import { CellBorders } from '../../../models/CellBorder';
import { Grouping } from '../../../models/Grouping';
import { ScanMetadata } from '../../../models/ScanMetadata';
import { PokeCardInfoCell } from '../Table';

const mockOnClick = vi.fn();
const DEFAULT_PROPS = {
  cellStyle: {
    dark: false,
    onClick: mockOnClick,
    cellBorders: new CellBorders(),
  },
  cardInstance: create(FakePokeCardInstanceWithOptions, {
    scanMetadata: new ScanMetadata({ confidence: 0.8 }),
  }),
  grouping: Grouping.NONE,
  displayConfidence: false,
};
const renderPokeCardInfoCell = (props: Partial<ComponentProps<typeof PokeCardInfoCell>> = {}) => {
  return render(<PokeCardInfoCell {...DEFAULT_PROPS} {...props} />);
};

describe('PokeCardInfoCell', () => {
  describe('when rendered', () => {
    it('renders card name', () => {
      const cardName = DEFAULT_PROPS.cardInstance.get('card').get('name');
      renderPokeCardInfoCell();
      expect(screen.getByText(cardName)).toBeInTheDocument();
    });

    it('renders PokeSetSymbol with size SM', () => {
      renderPokeCardInfoCell();
      expect(screen.getByRole('img').parentElement?.classList).toContain('set-symbol__sm');
    });

    it('triggers onClick when cell is clicked', () => {
      const { container } = renderPokeCardInfoCell();
      const cellElement = container.querySelector('.table__cell') as HTMLElement;
      cellElement.click();
      expect(mockOnClick).toHaveBeenCalled();
    });
  });

  describe('with confidence display', () => {
    it('shows confidence pill when displayConfidence is true and confidence exists', () => {
      const { container } = renderPokeCardInfoCell({
        displayConfidence: true,
      });
      expect(container.querySelector('.card-cell-confidence')).toBeInTheDocument();
    });

    it('hides confidence pill when displayConfidence is false', () => {
      const { container } = renderPokeCardInfoCell({
        displayConfidence: false,
      });
      expect(container.querySelector('.card-cell-confidence')).not.toBeInTheDocument();
    });
    it('handles undefined confidence correctly', () => {
      const { container } = renderPokeCardInfoCell({
        cardInstance: create(FakePokeCardInstanceWithOptions, { scanMetadata: undefined }),
        displayConfidence: true,
      });
      expect(container.querySelector('.card-cell-confidence')).not.toBeInTheDocument();
    });
  });
});

import plusIcon from '@iconify/icons-ic/baseline-plus';
import { fireEvent, render } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { MaterialIcon } from '../Table';

const mockOnClick = vi.fn();
const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
  materialIconText: plusIcon,
};

function renderMaterialIcon(props: Partial<ComponentProps<typeof MaterialIcon>> = {}) {
  return render(<MaterialIcon {...DEFAULT_PROPS} {...props} />);
}
describe('MaterialIcon', () => {
  it('renders correctly with basic props', () => {
    const { container } = renderMaterialIcon();
    const cellElement = container.querySelector('.table__cell');
    expect(cellElement).toBeInTheDocument();
  });
  it('renders Icon component', () => {
    const { container } = renderMaterialIcon();
    const iconElement = container.querySelector('svg');
    expect(iconElement).toBeInTheDocument();
  });
  it('triggers onClick when clicked', () => {
    const { container } = renderMaterialIcon();
    const cellElement = container.querySelector('.table__cell');
    fireEvent.click(cellElement!);
    expect(mockOnClick).toHaveBeenCalled();
  });
});

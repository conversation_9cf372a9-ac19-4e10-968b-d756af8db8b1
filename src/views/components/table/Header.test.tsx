import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { Header } from '../Table';

const DEFAULT_PROPS = {
  dark: false,
  cellBorders: new CellBorders(),
  title: 'Header Title',
};

function renderHeader(props: Partial<ComponentProps<typeof Header>> = {}) {
  return render(<Header {...DEFAULT_PROPS} {...props}></Header>);
}
describe('Header', () => {
  it('renders header title', () => {
    renderHeader();
    expect(screen.getByText(DEFAULT_PROPS.title)).toBeInTheDocument();
  });
  // Header component doesn't use generateCellClasses utility, so border and collection cell classes must be tested explicitly
  it('applies border classes correctly', () => {
    const customBorder = new CellBorders({
      top: true,
      left: true,
      right: false,
      bottom: true,
    });
    const { container } = renderHeader({ cellBorders: customBorder });
    const cellElement = container.querySelector('.table__header');
    expect(cellElement).toHaveClass('cell-border-top');
    expect(cellElement).toHaveClass('cell-border-left');
    expect(cellElement).toHaveClass('cell-border-bottom');
    expect(cellElement).not.toHaveClass('cell-border-right');
  });
  it('applies collection cell classes when collectionCell is true', () => {
    const { container } = renderHeader({ collectionCell: true });
    const cellElement = container.querySelector('.table__header');
    expect(cellElement).toHaveClass('collection-cell-justification');
  });
});

import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../../models/CellBorder';
import { SortingHeader } from '../Table';

const mockOnClick = vi.fn();
type DefaultOrderType = 'asc' | 'desc';
const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
  title: 'Sortable Header',
  sorting: 'asc',
  asc: 'asc',
  desc: 'desc',
  defaultOrder: 'asc' as DefaultOrderType,
};
function renderSortingHeader(props: Partial<ComponentProps<typeof SortingHeader>> = {}) {
  return render(<SortingHeader {...DEFAULT_PROPS} {...props}></SortingHeader>);
}
describe('SortingHeader', () => {
  it('renders sorting icons when sorting prop matches asc prop or desc prop', () => {
    const { container, rerender } = renderSortingHeader();
    expect(container.querySelector('svg')).toBeInTheDocument();
    rerender(<SortingHeader {...DEFAULT_PROPS} sorting="desc" />);
    expect(container.querySelector('svg')).toBeInTheDocument();
  });
  it('displays no icon when sorting matches neither asc nor desc', () => {
    const { container } = renderSortingHeader({ sorting: 'none', asc: 'asc', desc: 'desc' });
    const icon = container.querySelector('svg');
    expect(icon).toBeNull();
  });
  it('triggers onClick with correct sorting when clicked', () => {
    renderSortingHeader({ sorting: 'asc' });
    fireEvent.click(screen.getByText(DEFAULT_PROPS.title));
    expect(mockOnClick).toHaveBeenCalledWith('desc');
  });
});

import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it } from 'vitest';
import { FeaturePill, PillColor, PillSize } from './FeaturePill';

const DEFAULT_PROPS: ComponentProps<typeof FeaturePill> = {
  text: 'Test Pill',
  color: PillColor.BLUE,
};

const renderComponent = (props: Partial<ComponentProps<typeof FeaturePill>> = {}) => {
  return render(<FeaturePill {...DEFAULT_PROPS} {...props} />);
};

describe('FeaturePill', () => {
  describe('when rendered', () => {
    it('basic render', () => {
      const { container } = renderComponent();
      expect(screen.getByText(DEFAULT_PROPS.text)).toBeInTheDocument();
      expect(container.firstChild).toHaveClass(`feature-pill--${DEFAULT_PROPS.color}`);
    });
    it('applies extra small size class when specified', () => {
      const { container } = renderComponent({ size: PillSize.EXTRA_SMALL });
      expect(container.firstChild).toHaveClass('feature-pill-size--xs');
    });
    it(`applies correct class color`, () => {
      const { container } = renderComponent({ color: PillColor.BLUE });
      expect(container.firstChild).toHaveClass(`feature-pill--blue`);
    });
  });
});

import { faker } from '@faker-js/faker';
import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { FilterDisplay } from '../../../../src/models/filters/FilterDisplay';
import { SubtypeFilter } from '../../../../src/models/filters/SubtypeFilter';
import { validSubtypes } from '../../../../src/models/Subtypes';
import { SubtypeFilterComponent } from './SubtypeFilter';

const mockUpdate = vi.fn();
const mockOnClear = vi.fn();

// Use faker for realistic subtype data
const createMockSubtype = () => faker.helpers.arrayElement(validSubtypes);
const createSubtypeFilter = (subtype: string, include = true) => new SubtypeFilter({ subtype, include });

const mockSubtypes = Array.from({ length: 3 }, createMockSubtype);
const mockSubtypeFilter = createSubtypeFilter(mockSubtypes[0]);
const mockSubtypeFilters = mockSubtypes.map((subtype) => createSubtypeFilter(subtype));

const DEFAULT_PROPS = {
  subtypes: Immutable.OrderedMap<string, SubtypeFilter>(),
  displayType: FilterDisplay.PERMANENT,
  update: mockUpdate,
};

// Helper functions for DRY
const renderSubtypeFilter = (props: Partial<ComponentProps<typeof SubtypeFilterComponent>> = {}) => {
  return render(<SubtypeFilterComponent {...DEFAULT_PROPS} {...props} />);
};

const typeInSearch = (text: string) => fireEvent.change(screen.getByRole('textbox'), { target: { value: text } });

const createSubtypeMap = (filters: SubtypeFilter[]) => {
  return Immutable.OrderedMap<string, SubtypeFilter>(filters.map((filter) => [filter.get('subtype'), filter]));
};

const setupMockBlur = () => {
  const mockBlur = vi.fn();
  Object.defineProperty(document, 'activeElement', {
    value: { blur: mockBlur },
    writable: true,
  });
  return mockBlur;
};

describe('SubtypeFilter', () => {
  describe('when rendered', () => {
    it('displays all required elements', () => {
      renderSubtypeFilter();
      const selectInput = screen.getByRole('textbox');
      expect(screen.getByText('Subtype')).toBeInTheDocument();
      expect(selectInput).toBeInTheDocument();
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    describe('with disabled prop', () => {
      it('disables input', () => {
        renderSubtypeFilter({ disabled: true });
        const selectInput = screen.getByRole('textbox');
        expect(selectInput).toBeDisabled();
      });
    });
  });

  describe('with react-select', () => {
    it('accepts input typing', () => {
      renderSubtypeFilter();
      const selectInput = screen.getByRole('textbox');
      typeInSearch('test');
      expect(selectInput).toHaveValue('test');
    });

    it('should show dropdown list', async () => {
      const { container } = renderSubtypeFilter();
      const select = container.querySelector('.react-select__control');
      if (select) {
        expect(select).toBeInTheDocument();
        fireEvent.click(select);
        expect(screen.getByText(mockSubtypes[0])).toBeInTheDocument();
      }
    });

    it('handles clearing selections via keyboard', () => {
      const selectedMap = createSubtypeMap([mockSubtypeFilter]);
      renderSubtypeFilter({ subtypes: selectedMap });

      // Simulate clearing all values
      const selectInput = screen.getByRole('textbox');
      fireEvent.keyDown(selectInput, { key: 'Backspace', ctrlKey: true });
      expect(mockUpdate).toHaveBeenCalledWith(Immutable.OrderedMap());
    });
  });

  describe('with selected subtypes displayed', () => {
    it('shows selected subtypes', () => {
      const selectedMap = createSubtypeMap([mockSubtypeFilter]);
      renderSubtypeFilter({ subtypes: selectedMap });

      expect(screen.getByText(mockSubtypeFilter.get('subtype'))).toBeInTheDocument();
    });

    describe('when multiple subtypes selected', () => {
      it('displays all selected subtypes', () => {
        const selectedMap = createSubtypeMap([mockSubtypeFilters[0], mockSubtypeFilters[1]]);
        renderSubtypeFilter({ subtypes: selectedMap });

        expect(screen.getByText(mockSubtypeFilters[0].get('subtype'))).toBeInTheDocument();
        expect(screen.getByText(mockSubtypeFilters[1].get('subtype'))).toBeInTheDocument();
      });
    });

    describe('when selected subtype clicked', () => {
      it('toggles include state', () => {
        const selectedMap = createSubtypeMap([mockSubtypeFilter]);
        renderSubtypeFilter({ subtypes: selectedMap });

        fireEvent.click(screen.getByText(mockSubtypeFilter.get('subtype')));
        expect(mockUpdate).toHaveBeenCalled();
      });

      it('blurs active element', () => {
        const selectedMap = createSubtypeMap([mockSubtypeFilter]);
        const mockBlur = setupMockBlur();

        renderSubtypeFilter({ subtypes: selectedMap });
        fireEvent.click(screen.getByText(mockSubtypeFilter.get('subtype')));

        expect(mockBlur).toHaveBeenCalled();
      });

      it('applies strikethrough for excluded subtypes', () => {
        const excludedFilter = createSubtypeFilter(mockSubtypes[0], false);
        const selectedMap = createSubtypeMap([excludedFilter]);

        renderSubtypeFilter({ subtypes: selectedMap });

        const subtypeElement = screen.getByText(excludedFilter.get('subtype'));
        expect(subtypeElement).toHaveStyle('text-decoration-line: line-through');
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders Clear button', () => {
      renderSubtypeFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    it('renders Remove button', () => {
      renderSubtypeFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('triggers onClear prop', () => {
      renderSubtypeFilter({ onClear: mockOnClear });
      fireEvent.click(screen.getByText('Clear'));
      expect(mockOnClear).toHaveBeenCalled();
    });

    describe('when clear button clicked', () => {
      it('clears all subtypes', () => {
        const selectedMap = createSubtypeMap([mockSubtypeFilter]);
        renderSubtypeFilter({ subtypes: selectedMap });

        fireEvent.click(screen.getByText('Clear'));
        expect(mockUpdate).toHaveBeenCalledWith(Immutable.OrderedMap());
      });
    });
  });

  describe('with menu portal prop', () => {
    it('accepts portal element', () => {
      const portal = document.createElement('div');
      renderSubtypeFilter({ menuPortal: portal });
      const selectInput = screen.getByRole('textbox');
      expect(selectInput).toBeInTheDocument();
    });
  });

  describe('with option generation', () => {
    it('has access to validSubtypes', () => {
      renderSubtypeFilter();

      // Component should render without errors when validSubtypes is available
      const selectInput = screen.getByRole('textbox');
      expect(selectInput).toBeInTheDocument();
      expect(validSubtypes.length).toBeGreaterThan(0);
    });

    it('works with selected subtypes', () => {
      const selectedMap = createSubtypeMap(mockSubtypeFilters);
      renderSubtypeFilter({ subtypes: selectedMap });

      // Should render selected subtypes
      expect(screen.getByText(mockSubtypeFilters[0].get('subtype'))).toBeInTheDocument();
    });
  });

  describe('with humanized labels', () => {
    it('displays humanized subtype names', () => {
      const testSubtype = 'test-subtype';
      const filter = createSubtypeFilter(testSubtype);
      const selectedMap = createSubtypeMap([filter]);

      renderSubtypeFilter({ subtypes: selectedMap });

      // The component should show the humanized version "Test Subtype"
      expect(screen.getByText('Test Subtype')).toBeInTheDocument();
    });
  });
});

import check from '@iconify/icons-ic/check';
import expand_less from '@iconify/icons-ic/expand-less';
import expand_more from '@iconify/icons-ic/expand-more';
import link from '@iconify/icons-ic/link';
import { Icon, IconifyIcon } from '@iconify/react';
import classNames from 'classnames';
import * as Immutable from 'immutable';
import * as React from 'react';
import { CardInstance } from '../../models/CardInstances';
import { Card } from '../../models/Cards';
import { CellBorders } from '../../models/CellBorder';
import { Deck } from '../../models/Decks';
import { Grouping } from '../../models/Grouping';
import { LorcanaCardInstance } from '../../models/lorcana/LorcanaCardInstances';
import { PokeCardInstance } from '../../models/pokemon/PokeCardInstances';
import { Tag } from '../../models/Tags';
import { YugiCardInstance } from '../../models/yugioh/YugiCardInstances';
import { Checkbox, IProps as ICheckboxProps } from './Checkbox';
import { FeaturePill, PillColor, PillSize } from './FeaturePill';
import { LorcanaSetSymbol } from './lorcana/LorcanaSetSymbol';
import { PokeSetSymbol } from './pokemon/PokeSetSymbol';
import { ISetSymbolProps, SetSymbol, SetSymbolSize } from './SetSymbol';
import { YugiSetSymbol } from './yugioh/YugiSetSymbol';

export enum ColumnSize {
  MAX_CONTENT = 'max-content',
  MINMAX_100 = 'minmax(max-content, 100%)',
  MINMAX_1FR = 'minmax(max-content, 1fr)',
}

export class TableConfiguration {
  sizes: string[];

  constructor(sizes: string[]) {
    this.sizes = sizes;
  }

  static repeat(count: number, size: ColumnSize) {
    return `repeat(${count}, ${size})`;
  }

  style(): React.CSSProperties {
    return {
      gridTemplateColumns: this.sizes.reduce((str, size) => `${str} ${size}`, ''),
    };
  }
}

// TODO: This entire file is an absolute mess - build the hierarchy back up from scratch
interface ICellProps {
  dark: boolean;
  large?: boolean;
  collectionCell?: boolean;
  centerCell?: boolean;
  cardNameFormatting?: boolean;
  cellBorders: CellBorders;
  onClick?: () => void;
}

export function generateCellClasses(props: ICellProps): string {
  return classNames({
    table__cell: true,
    'collection-cell-justification': props.collectionCell === undefined ? false : props.collectionCell,
    cell__clickable: props.onClick !== undefined,
    cell__dark: props.dark,
    cell__large: props.large === undefined ? false : props.large,
    'cell-border-top': props.cellBorders.get('top'),
    'cell-border-left': props.cellBorders.get('left'),
    'cell-border-right': props.cellBorders.get('right'),
    'cell-border-bottom': props.cellBorders.get('bottom'),
  });
}

export function generateInnerClasses(props: ICellProps): string {
  return classNames({
    'collection-cell-inner':
      (props.collectionCell === undefined ? false : props.collectionCell) &&
      !(props.cardNameFormatting === undefined ? false : props.cardNameFormatting),
    'cell-center': props.centerCell === undefined ? false : props.centerCell,
    'card-name-cell-text': props.cardNameFormatting === undefined ? false : props.cardNameFormatting,
  });
}

interface ITextCell {
  text: string;
}

export function TextCell(props: ICellProps & ITextCell): JSX.Element {
  return (
    <div className={generateCellClasses(props)} onClick={props.onClick}>
      <p className={generateInnerClasses(props)}>{props.text}</p>
    </div>
  );
}

interface ICheckProps {
  checked: boolean;
}

export function CheckCell(props: ICellProps & ICheckProps): JSX.Element {
  return (
    <div className={generateCellClasses(props)} onClick={props.onClick}>
      <p className={generateInnerClasses(props)}>
        {props.checked ? <Icon height={'18px'} width={'18px'} icon={check} /> : '-'}
      </p>
    </div>
  );
}

export function CheckBoxCell(props: ICellProps & ICheckboxProps): JSX.Element {
  const innerClasses = classNames(generateInnerClasses(props), {
    'checkbox-override': true,
  });
  return (
    <div className={generateCellClasses(props)}>
      <div className={innerClasses}>
        <Checkbox
          className={props.className}
          checked={props.checked}
          multiple={props.multiple}
          disabled={props.disabled}
          onChange={props.onChange}
        />
      </div>
    </div>
  );
}

interface IMTGCardInstanceInfoCell {
  cellStyle: ICellProps;
  cardInstance: CardInstance;
  grouping: Grouping;
  displayConfidence?: boolean;
}

export function MTGCardInstanceInfoCell(props: IMTGCardInstanceInfoCell): JSX.Element {
  const confidence = props.displayConfidence ? props.cardInstance.get('scanMetadata')?.get('confidence') : undefined;
  return (
    <CardInfoCell
      cellStyle={props.cellStyle}
      cardName={props.cardInstance.get('cardName')}
      collectorNumber={props.cardInstance.get('cardCollectorNumber')}
      setName={props.cardInstance.get('cardSetName')}
      confidence={confidence}
      grouping={props.grouping}
    >
      <SetSymbol
        setName={props.cardInstance.get('cardSetName')}
        setCode={props.cardInstance.get('cardSetCode')}
        hoverText={false}
        rarity={props.cardInstance.get('cardRarity')}
        collectorNumber={props.cardInstance.get('cardCollectorNumber')}
        size={SetSymbolSize.SM}
      />
    </CardInfoCell>
  );
}

interface IMTGCardInfoCell {
  cellStyle: ICellProps;
  card: Card;
  grouping: Grouping;
  button?: React.ReactNode;
}

export function MTGCardInfoCell(props: IMTGCardInfoCell): JSX.Element {
  return (
    <CardInfoCell
      cellStyle={props.cellStyle}
      cardName={props.card.get('name')}
      collectorNumber={props.card.get('collectorNumber')}
      setName={props.card.get('setName')}
      grouping={props.grouping}
      button={props.button}
    >
      <SetSymbol
        setName={props.card.get('setName')}
        setCode={props.card.get('setCode')}
        hoverText={false}
        rarity={props.card.get('rarity')}
        collectorNumber={props.card.get('collectorNumber')}
        size={SetSymbolSize.SM}
      />
    </CardInfoCell>
  );
}

interface IPokeCardInfoCell {
  cellStyle: ICellProps;
  cardInstance: PokeCardInstance;
  grouping: Grouping;
  displayConfidence?: boolean;
}

export function PokeCardInfoCell(props: IPokeCardInfoCell): JSX.Element {
  const card = props.cardInstance.get('card');
  const confidence = props.displayConfidence ? props.cardInstance.get('scanMetadata')?.get('confidence') : undefined;
  return (
    <CardInfoCell
      cellStyle={props.cellStyle}
      cardName={card.get('name')}
      collectorNumber={card.get('collectorNumber')}
      setName={card.get('setName')}
      confidence={confidence}
      grouping={props.grouping}
    >
      <PokeSetSymbol
        setName={card.get('setName')}
        setUUID={card.get('setUUID')}
        hoverText={false}
        rarity={card.get('rarity')}
        collectorNumber={card.get('collectorNumber')}
        size={SetSymbolSize.SM}
      />
    </CardInfoCell>
  );
}

interface IYugiCardInfoCell {
  cellStyle: ICellProps;
  cardInstance: YugiCardInstance;
  grouping: Grouping;
  displayConfidence?: boolean;
}

export function YugiCardInfoCell(props: IYugiCardInfoCell): JSX.Element {
  const card = props.cardInstance.get('card');
  const cardSet = props.cardInstance.get('cardSet');
  const set = cardSet?.get('set');
  const confidence = props.displayConfidence ? props.cardInstance.get('scanMetadata')?.get('confidence') : undefined;

  return (
    <CardInfoCell
      cellStyle={props.cellStyle}
      cardName={card.get('name')}
      collectorNumber={cardSet?.get('code')}
      setName={set?.get('name') || 'Unknown'}
      confidence={confidence}
      grouping={props.grouping}
    >
      <YugiSetSymbol
        setName={set?.get('name') || 'Unknown'}
        setUUID={set?.get('uuid')}
        hoverText={false}
        rarity={cardSet?.get('rarity')}
        collectorNumber={cardSet?.get('code') || '0'}
        size={SetSymbolSize.SM}
      />
    </CardInfoCell>
  );
}

interface ILorcanaCardInfoCell {
  cellStyle: ICellProps;
  cardInstance: LorcanaCardInstance;
  grouping: Grouping;
  displayConfidence?: boolean;
}

export function LorcanaCardInfoCell(props: ILorcanaCardInfoCell): JSX.Element {
  const card = props.cardInstance.get('card');
  const confidence = props.displayConfidence ? props.cardInstance.get('scanMetadata')?.get('confidence') : undefined;
  return (
    <CardInfoCell
      cellStyle={props.cellStyle}
      cardName={card.get('name')}
      collectorNumber={card.get('collectorNumber')}
      setName={card.get('setName')}
      confidence={confidence}
      grouping={props.grouping}
    >
      <LorcanaSetSymbol
        setName={card.get('setName')}
        setUUID={card.get('setUUID')}
        hoverText={false}
        rarity={card.get('rarity')}
        collectorNumber={card.get('collectorNumber')}
        size={SetSymbolSize.SM}
      />
    </CardInfoCell>
  );
}

interface ICardInfoCell {
  cardName: string;
  collectorNumber?: string;
  setName: string;
  confidence?: number;
  grouping: Grouping;
  cellStyle: ICellProps;
  children: React.ReactNode;
  button?: React.ReactNode;
}

function CardInfoCell(props: ICardInfoCell): JSX.Element {
  let subtitle = <></>;
  if (props.grouping !== Grouping.NAME) {
    let subtitleText: string;
    if (props.collectorNumber) {
      subtitleText = `#${props.collectorNumber} • ${props.setName}`;
    } else {
      subtitleText = props.setName;
    }

    subtitle = <div className="card-cell-subheading">{subtitleText}</div>;
  }

  const pillColor = props.confidence && props.confidence > 0.6 ? PillColor.GREEN : PillColor.RED;

  return (
    <div className={generateCellClasses(props.cellStyle)} onClick={props.cellStyle.onClick}>
      <div className="card-cell-content-container">
        <div className="card-cell-card-info">
          {props.children}
          <div style={{ marginLeft: '1rem' }}>
            <div className="card-cell-heading">{props.cardName}</div>
            {subtitle}
          </div>
        </div>
        {props.confidence !== undefined && (
          <div className="card-cell-confidence">
            <FeaturePill
              text={`${Math.round(props.confidence * 100)}%`}
              color={pillColor}
              size={PillSize.EXTRA_SMALL}
            />
          </div>
        )}
        {props.button && <div className="card-cell-hover-button">{props.button}</div>}
      </div>
    </div>
  );
}

// This will likely be used when we implement similar card tables, hence why this is going in the generic Table.tsx file.
export function SetIconCell(props: ICellProps & ISetSymbolProps): JSX.Element {
  return (
    <div className={generateCellClasses(props)} onClick={props.onClick}>
      <div className={generateInnerClasses(props)}>
        <SetSymbol
          setName={props.setName}
          setCode={props.setCode}
          hoverText={props.hoverText}
          rarity={props.rarity}
          collectorNumber={props.collectorNumber}
        />
      </div>
    </div>
  );
}

interface IIconCell {
  materialIconText: IconifyIcon;
}

export function MaterialIcon(props: ICellProps & IIconCell): JSX.Element {
  return (
    <div className={generateCellClasses(props)} onClick={props.onClick}>
      <div className={generateInnerClasses(props)}>
        <Icon style={{ marginTop: '0.3rem' }} height={'24px'} width={'24px'} icon={props.materialIconText} />
      </div>
    </div>
  );
}

interface IHeaderProps {
  title: string;
  cellBorders: CellBorders;
  collectionCell?: boolean;
}

export function Header(props: IHeaderProps): JSX.Element {
  const className = classNames({
    table__header: true,
    'collection-cell-justification': props.collectionCell === undefined ? false : props.collectionCell,
    'cell-border-top': props.cellBorders.get('top'),
    'cell-border-left': props.cellBorders.get('left'),
    'cell-border-right': props.cellBorders.get('right'),
    'cell-border-bottom': props.cellBorders.get('bottom'),
  });
  return <div className={className}>{props.title}</div>;
}

interface ISortingHeaderProps extends IHeaderProps {
  sorting: string;
  asc: string;
  desc: string;
  onClick: (sorting: string) => void;
  defaultOrder?: 'asc' | 'desc';
}

export function SortingHeader(props: ISortingHeaderProps): JSX.Element {
  const defaultOrder = props.defaultOrder === undefined ? 'desc' : props.defaultOrder;
  const className = classNames({
    table__header: true,
    'collection-cell-justification': props.collectionCell === undefined ? false : props.collectionCell,
    cell__clickable: props.onClick !== undefined,
    'cell-border-top': props.cellBorders.get('top'),
    'cell-border-left': props.cellBorders.get('left'),
    'cell-border-right': props.cellBorders.get('right'),
    'cell-border-bottom': props.cellBorders.get('bottom'),
  });
  return (
    <div
      className={className}
      onClick={() => props.onClick(generateSorting(props.sorting, props.asc, props.desc, defaultOrder))}
    >
      {props.title}
      {generateAscDescIcon()}
    </div>
  );

  function generateAscDescIcon() {
    if (props.sorting === props.asc) {
      return <Icon height={'24px'} width={'24px'} icon={expand_less} />;
    } else if (props.sorting === props.desc) {
      return <Icon height={'24px'} width={'24px'} icon={expand_more} />;
    } else {
      return null;
    }
  }
}

// Handles multiple sorting types combined into one row, can't be changed manually.
interface IMultiSortingHeaderProps {
  title: string;
  sorting: string;
  asc: string[];
  desc: string[];
  cellBorders: CellBorders;
  collectionCell?: boolean;
  large?: boolean;
}

export function MultiSortingHeader(props: IMultiSortingHeaderProps): JSX.Element {
  const className = classNames({
    table__header: true,
    'collection-cell-justification': props.collectionCell === undefined ? false : props.collectionCell,
    'cell-border-top': props.cellBorders.get('top'),
    'cell-border-left': props.cellBorders.get('left'),
    'cell-border-right': props.cellBorders.get('right'),
    'cell-border-bottom': props.cellBorders.get('bottom'),
  });
  const asc = props.asc.some((value: string) => {
    return value === props.sorting;
  });
  const desc =
    asc === true
      ? false
      : props.desc.some((value: string) => {
          return value === props.sorting;
        });

  return (
    <div className={className}>
      {props.title}
      {generateMultiAscDescIcon()}
    </div>
  );

  function generateMultiAscDescIcon() {
    if (props.asc.some((value: string) => value === props.sorting)) {
      return <Icon height={'24px'} width={'24px'} icon={expand_less} />;
    } else if (props.desc.some((value: string) => value === props.sorting)) {
      return <Icon height={'24px'} width={'24px'} icon={expand_more} />;
    } else {
      return null;
    }
  }
}

export function generateSorting(sorting: string, asc: string, desc: string, defaultOrder: 'asc' | 'desc') {
  switch (defaultOrder) {
    case 'asc':
      return sorting === asc ? desc : asc;
    case 'desc':
      return sorting === desc ? asc : desc;
  }
}

interface ISelectCell {
  value: string;
  icon?: IconifyIcon;
}

interface ISelectEditableCell {
  grouping: Grouping; // TODO: WTF is this doing here?
  children: React.ReactNode;
  onChange(evt: React.ChangeEvent<HTMLSelectElement>): any;
}

// TODO: Deprecate - use SelectorCell
export function SelectCell(props: ICellProps & ISelectCell & ISelectEditableCell): JSX.Element {
  const className = classNames({
    'icon-container--table': props.icon !== undefined && props.grouping !== Grouping.NAME,
    'icon-container--table-by-name': props.icon !== undefined && props.grouping === Grouping.NAME,
    'table__cell flex cell__align-items select__cell': true,
    cell__dark: props.dark,
    'cell-border-top': props.cellBorders.get('top'),
    'cell-border-left': props.cellBorders.get('left'),
    'cell-border-right': props.cellBorders.get('right'),
    'cell-border-bottom': props.cellBorders.get('bottom'),
  });
  return (
    <div className={className}>
      {props.icon === undefined ? null : <Icon height={'18px'} width={'18px'} icon={props.icon} />}
      <select
        className="select"
        style={{ marginLeft: '1rem', marginRight: '1rem' }}
        value={props.value}
        onChange={(evt: React.ChangeEvent<HTMLSelectElement>) => {
          props.onChange(evt);
        }}
      >
        {props.children}
      </select>
    </div>
  );
}

interface ISelectorCell {
  children: React.ReactNode;
}
// Assumes a *Selector.tsx component in props.children
export function SelectorCell(props: ICellProps & ISelectorCell): JSX.Element {
  const className = classNames({
    'table__cell flex cell__align-items select__cell': true,
    cell__dark: props.dark,
    'cell-border-top': props.cellBorders.get('top'),
    'cell-border-left': props.cellBorders.get('left'),
    'cell-border-right': props.cellBorders.get('right'),
    'cell-border-bottom': props.cellBorders.get('bottom'),
  });
  return <div className={className}>{props.children}</div>;
}

interface IAmountCell {
  amount: number;
}

export function AmountCell(props: ICellProps & IAmountCell): JSX.Element {
  return (
    <div className={generateCellClasses(props)} onClick={props.onClick}>
      <div className="amount-container">
        <span className="amount">{props.amount}</span>
      </div>
    </div>
  );
}

interface IDeckLinkProps {
  deck: Deck;
  cardInstance: CardInstance;
  isConfirming: boolean;
  onRequestConfirmation: () => void;
  onLink: () => void;
}

export function DeckLinkCell(props: ICellProps & IDeckLinkProps) {
  const resources = props.cardInstance.get('linkedResources');
  const linkedDeck = resources === undefined ? undefined : resources.get('deck');
  const linkedDeckID = linkedDeck === undefined ? 0 : linkedDeck.get('id');
  const isLinked = linkedDeckID > 0;
  const linkedToThisDeck = linkedDeckID === props.deck.get('id');
  const linkColour = isLinked ? (linkedToThisDeck ? '#1E1E1E' : '#CC3333') : '#CDCDCD';
  return (
    <div
      key={props.cardInstance.get('id')}
      className={generateCellClasses(props)}
      style={{ cursor: 'pointer' }}
      onClick={(evt: React.SyntheticEvent<HTMLElement>) => {
        evt.preventDefault();
        evt.stopPropagation();
        if (!props.isConfirming && isLinked && !linkedToThisDeck) {
          props.onRequestConfirmation();
        } else {
          props.onLink();
        }
      }}
    >
      {props.isConfirming ? (
        <div className="hover-hint-container">
          <div className="hover-hint">
            <Icon className="table-deletion-hint" height={'18px'} width={'18px'} icon={check} />
            <div className="hover-hint-tooltip-container">
              <div className="hover-hint-tooltip" style={{ position: 'fixed' }}>
                {`Are you sure?`}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <Icon color={linkColour} height={'18px'} width={'18px'} icon={link} />
      )}
    </div>
  );
}

interface ITagCellProps {
  cardInstance: CardInstance;
  cardInstanceTags: Immutable.Map<number, Immutable.List<Tag>>;
}

export function TagsCell(props: ICellProps & ITagCellProps) {
  return (
    <div
      className={generateCellClasses(props)}
      style={{
        overflowX: 'scroll',
        overflowY: 'hidden',
      }}
    >
      {props.cardInstanceTags.get(props.cardInstance.get('id'))
        ? props.cardInstanceTags.get(props.cardInstance.get('id')).size
          ? props.cardInstanceTags.get(props.cardInstance.get('id')).map((tag: Tag) => {
              return (
                <div
                  key={'tag-' + tag.get('name')}
                  className="tags-builder-tag"
                  style={{ cursor: 'default', marginTop: '0.75rem' }}
                >
                  #{tag.get('name')}
                </div>
              );
            })
          : '-'
        : 'Loading...'}
    </div>
  );
}

interface IChildProps {
  children: React.ReactNode;
}

export function ParentCell(props: ICellProps & IChildProps): JSX.Element {
  return (
    <div className={generateCellClasses(props)} onClick={props.onClick}>
      {props.children}
    </div>
  );
}

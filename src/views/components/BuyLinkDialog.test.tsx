import { faker } from '@faker-js/faker';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { create } from '../../../tests/fake/Fake';
import { FakeCardKingdomService, FakeCartData, FakeTCGPlayerService } from '../../../tests/fake/FakeCartData';
import { FakeUser } from '../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../tests/test-utils';
import * as CartActions from '../../actions/CartActions';
import { TextFormat } from '../../helpers/fmt';
import { CartTableGrouping } from '../../lib/carts/CartService';
import { CartLocation } from '../../models/CartButton';
import { CartCardRecord, CartData } from '../../models/CartCards';
import { BuyLinkDialog } from './BuyLinkDialog';

interface CreateCartDataOptions {
  recordCount?: number;
  cartDataWithQuantities?: boolean;
}

const createCartData = ({ recordCount = 3, cartDataWithQuantities = false }: CreateCartDataOptions = {}) => {
  return create(FakeCartData, {
    recordCount,
    ...(cartDataWithQuantities && {
      quantity: faker.number.int({ min: 1, max: 4 }),
      ownedCount: faker.number.int({ min: 0, max: 2 }),
      deckCount: faker.number.int({ min: 0, max: 2 }),
    }),
  });
};

vi.mock('../../actions/CartActions');

// Create fake service instances for testing
const fakeCardKingdomService = new FakeCardKingdomService();
const fakeTCGPlayerService = new FakeTCGPlayerService();

// Mock the service classes to use our fake implementations
vi.mock('../../lib/carts/CardKingdom', () => ({
  CardKingdomService: vi.fn().mockImplementation(() => fakeCardKingdomService),
}));

vi.mock('../../lib/carts/TCGPlayer', () => ({
  TCGPlayerService: vi.fn().mockImplementation(() => fakeTCGPlayerService),
}));

// Mock owned cart data for quantity calculations in useEffect
const mockOwnedCartData = createCartData({ recordCount: 2, cartDataWithQuantities: true }).get('data') as Immutable.Map<
  string,
  CartCardRecord
>;
vi.mocked(CartActions.getCardQuantities).mockResolvedValue(Immutable.Map(mockOwnedCartData));

interface GetMockCartStateDataParams extends CreateCartDataOptions {
  location?: CartLocation;
  cardDataProps?: CartData;
}

/*
 * Helper simulates updateCartData's async cart extension with quantities and deck initialization
 * This is necessary because updateCartData is called in useEffect and manipulates the cartData state
 */
const getMockCartStateData = ({
  location = CartLocation.COLLECTION,
  recordCount = 3,
  cartDataWithQuantities = true,
  cardDataProps,
}: GetMockCartStateDataParams) => {
  const cartData = cardDataProps ?? createCartData({ recordCount, cartDataWithQuantities });
  let mockCartData = CartCardRecord.extendMapFromQuantity(cartData, mockOwnedCartData);

  if (location === CartLocation.DECKVIEWER) {
    mockCartData = CartCardRecord.initializeDeckTable(cartData, mockCartData);
  }
  return mockCartData;
};

const getTotalCards = (cartData: CartData) => {
  return cartData.get('data').reduce((reduction: number, value: CartCardRecord) => {
    const desired = value.get('quantity');
    return desired ? reduction + desired : reduction;
  }, 0);
};

const createLinkedCards = ({ cardNames = ['Lightning Bolt', 'Counterspell'] } = {}) => {
  const cardEntries = cardNames.map((name) => [name, faker.number.int({ min: 1, max: 4 })] as [string, number]);
  return Immutable.Map<string, number>(cardEntries);
};

const createUser = ({ loggedIn = true }: { loggedIn?: boolean } = {}) => {
  return create(FakeUser, {
    id: loggedIn ? faker.number.int({ min: 1, max: 1000 }) : 0,
    username: loggedIn ? faker.internet.userName() : '',
  });
};

const DEFAULT_PROPS = {
  me: createUser({ loggedIn: true }),
  isPublic: false,
  isOpen: true,
  location: CartLocation.COLLECTION,
  cartData: createCartData(),
  onDismiss: vi.fn(),
};

const renderBuyLinkDialog = (props: Partial<Omit<ComponentProps<typeof BuyLinkDialog>, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(BuyLinkDialog, {
    ...DEFAULT_PROPS,
    ...props,
  });
};

// Note: cartData === undefined branches are not testable because component crashes in useEffect when cartData is undefined :(
describe('BuyLinkDialog', () => {
  describe('dialog visibility', () => {
    it('shows main sections when open', () => {
      renderBuyLinkDialog();

      expect(screen.getByText('Buy Cards')).toBeInTheDocument();
      expect(screen.getByText('Purchasing')).toBeInTheDocument();
      expect(screen.getByText('Service')).toBeInTheDocument();
      // Multiple "Group By" text elements expected
      expect(screen.getAllByText('Group By')).toHaveLength(2);
    });

    test('calls onDismiss when cancel clicked', async () => {
      const onDismiss = vi.fn();
      renderBuyLinkDialog({ onDismiss });
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      expect(onDismiss).toHaveBeenCalledTimes(1);
    });

    it('hides when closed', () => {
      renderBuyLinkDialog({ isOpen: false });
      expect(screen.queryByText('Buy Cards')).not.toBeInTheDocument();
    });
  });

  describe('user authentication', () => {
    it('calls getCardQuantities when logged in', async () => {
      renderBuyLinkDialog();

      await waitFor(() => {
        expect(CartActions.getCardQuantities).toHaveBeenCalled();
      });
    });
  });

  describe('cart locations', () => {
    beforeEach(() => {
      vi.spyOn(CartCardRecord, 'initializeDeckTable');
    });

    it('handles collection location', async () => {
      renderBuyLinkDialog({
        location: CartLocation.COLLECTION,
      });
      await waitFor(() => {
        expect(screen.queryByText('Buy Cards')).toBeInTheDocument();
        expect(CartCardRecord.initializeDeckTable).not.toHaveBeenCalled();
      });
    });

    it('handles deck viewer location', async () => {
      renderBuyLinkDialog({
        location: CartLocation.DECKVIEWER,
      });
      await waitFor(() => {
        expect(screen.queryByText('Buy Cards')).toBeInTheDocument();
      });
      expect(CartCardRecord.initializeDeckTable).toHaveBeenCalled();
    });
  });

  describe('service dropdown', () => {
    it('renders service dropdown', () => {
      const { container } = renderBuyLinkDialog();
      const serviceHeadElement = container.querySelector('.info-heading');

      // if we migrate to new setup, we can use the service section directly
      const serviceSection = serviceHeadElement?.nextElementSibling;

      expect(serviceSection).toBeInTheDocument();
    });

    test('changes service selection', async () => {
      const cartData = createCartData({
        recordCount: 2,
        cartDataWithQuantities: true,
      });

      const { container } = renderBuyLinkDialog({
        isOpen: true,
        location: CartLocation.COLLECTION,
        me: createUser({ loggedIn: true }),
        cartData,
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });
      const selectedService = container.querySelector('.react-select-thin__single-value');

      // Default is Card Kingdom
      expect(selectedService?.textContent).toBe('Card Kingdom');

      fireEvent.mouseDown(screen.getByRole('textbox'));
      fireEvent.click(screen.getByText('TCGplayer'));

      // Check if service changed
      expect(selectedService?.textContent).toBe('TCGplayer');
    });
  });

  describe('grouping select', () => {
    test('handles grouping changes', async () => {
      const { container } = renderBuyLinkDialog();

      await waitFor(() => {
        const selectElement = container.querySelector('select') as HTMLSelectElement;
        expect(selectElement).toBeInTheDocument();

        // Trigger onChange callback with string values
        fireEvent.change(selectElement, { target: { value: 'Set' } });

        // Verify select element still exists after change handling
        expect(selectElement).toBeInTheDocument();
      });
    });

    test('handles different grouping options', async () => {
      const { container } = renderBuyLinkDialog();

      await waitFor(() => {
        const selectElement = container.querySelector('select') as HTMLSelectElement;

        // Test various grouping option changes
        fireEvent.change(selectElement, { target: { value: 'Set' } });
        fireEvent.change(selectElement, { target: { value: 'Printing' } });
        fireEvent.change(selectElement, { target: { value: 'Name' } });

        expect(selectElement).toBeInTheDocument();
      });
    });

    it('displays grouping options', async () => {
      const { container } = renderBuyLinkDialog();

      await waitFor(() => {
        // Verify select has expected options
        const selectElement = container.querySelector('select');
        expect(selectElement).toBeInTheDocument();

        if (selectElement) {
          const options = selectElement.querySelectorAll('option');
          expect(options.length).toBeGreaterThan(0);
        }
      });
    });
  });

  describe('with BuyLinkIconButtons', () => {
    it('renders buttons', async () => {
      renderBuyLinkDialog({
        location: CartLocation.COLLECTION,
      });

      await waitFor(() => {
        expect(screen.getByText('Reset')).toBeInTheDocument();
        expect(screen.getByText('Complete Playset')).toBeInTheDocument();
        expect(screen.queryByText('Purchase all cards')).not.toBeInTheDocument();
      });
    });

    test('handles purchase all cards', async () => {
      vi.spyOn(CartCardRecord, 'purchaseAllCards');
      const cartData = createCartData({ recordCount: 2, cartDataWithQuantities: true });

      renderBuyLinkDialog({
        isOpen: true,
        location: CartLocation.DECKVIEWER,
        me: createUser({ loggedIn: true }),
        cartData,
      });
      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      const purchaseAllButton = screen.getByText('Purchase all cards');
      expect(purchaseAllButton).toBeInTheDocument();

      // Click to trigger onPurchaseAllCards function
      fireEvent.click(purchaseAllButton);

      expect(CartCardRecord.purchaseAllCards).toHaveBeenCalled();
    });

    it('handles purchase unlinked cards', async () => {
      const cartData = createCartData({ recordCount: 3, cartDataWithQuantities: true });

      const cartDataState = getMockCartStateData({ cardDataProps: cartData, location: CartLocation.DECKVIEWER });
      const totalCards = getTotalCards(cartDataState);

      const getFirstCardName = cartDataState.get('data').first().get('cartCard').get('name');
      const getFirstCardDeckCount = cartDataState.get('data').first().get('deckCount');
      const linkedCards = createLinkedCards({ cardNames: [getFirstCardName] });

      renderBuyLinkDialog({
        cartData,
        linkedCards,
        location: CartLocation.DECKVIEWER,
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      // Verify that the total number of cards is displayed
      expect(screen.getByText(TextFormat.pluralizedCardsCapitalized(totalCards))).toBeInTheDocument();

      // Clear mock to ensure getCardQuantities is called only once
      (CartActions.getCardQuantities as any).mockClear();

      const purchaseUnlinkedButton = screen.getByText('Purchase unlinked cards');
      expect(purchaseUnlinkedButton).toBeInTheDocument();

      fireEvent.click(purchaseUnlinkedButton);

      expect(CartActions.getCardQuantities).toHaveBeenCalledTimes(1);
      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      // card count should be the total cards minus the first card deck count
      expect(
        screen.getByText(TextFormat.pluralizedCardsCapitalized(totalCards - (getFirstCardDeckCount ?? 0))),
      ).toBeInTheDocument();
    });

    it('hides deck viewer buttons when not logged in', async () => {
      renderBuyLinkDialog({
        isOpen: true,
        location: CartLocation.DECKVIEWER,
        me: createUser({ loggedIn: false }),
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      expect(screen.queryByText('Purchase all cards')).not.toBeInTheDocument();
      expect(screen.queryByText('Purchase unlinked cards')).not.toBeInTheDocument();
    });

    test('handles complete playset', async () => {
      const cartData = createCartData({
        recordCount: 3,
        cartDataWithQuantities: true,
      });

      const cartDataState = getMockCartStateData({ cardDataProps: cartData, location: CartLocation.COLLECTION });
      const initialTotalCards = getTotalCards(cartDataState);

      // Calculate expected total after complete playset operation
      // Complete playset groups by name (default grouping), sums ownedCount per group,
      // then sets quantity to max(0, 4 - totalOwnedCount) for each group
      const groupedByName = cartDataState
        .get('data')
        .groupBy((record: CartCardRecord) => record.get('cartCard').get('name'));

      let expectedTotalAfterPlayset = 0;
      groupedByName.forEach((records: Immutable.Iterable<string, CartCardRecord>) => {
        const totalOwnedCount = records.reduce((sum: number, record: CartCardRecord) => {
          const ownedCount = record.get('ownedCount') || 0;
          return sum + ownedCount;
        }, 0);
        const playsetQuantity = Math.max(0, 4 - totalOwnedCount);
        expectedTotalAfterPlayset += playsetQuantity;
      });

      renderBuyLinkDialog({
        location: CartLocation.COLLECTION,
        cartData,
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      // Verify that the initial total number of cards is displayed
      expect(screen.getByText(TextFormat.pluralizedCardsCapitalized(initialTotalCards))).toBeInTheDocument();

      const completePlaysetButton = screen.getByText('Complete Playset');
      expect(completePlaysetButton).toBeInTheDocument();

      fireEvent.click(completePlaysetButton);

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      // Verify the new total after complete playset operation
      expect(screen.getByText(TextFormat.pluralizedCardsCapitalized(expectedTotalAfterPlayset))).toBeInTheDocument();

      // Verify buttons are still present after operation
      expect(screen.getByText('Complete Playset')).toBeInTheDocument();
      expect(screen.getByText('Reset')).toBeInTheDocument();
    });
  });

  describe('cart table', () => {
    it('renders table with columns and cards', async () => {
      const cartData = createCartData({ recordCount: 2 });
      const { container } = renderBuyLinkDialog({
        isOpen: true,
        cartData,
      });

      await waitFor(() => {
        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Card Name')).toBeInTheDocument();

        // Check for quantity inputs
        const quantityInput = container.querySelectorAll('input.buy-cards-input[type="number"]');
        expect(quantityInput.length).toBe(cartData.get('data').count());

        // Verify all cards are in the table
        cartData.get('data').forEach((value: CartCardRecord) => {
          expect(screen.getByText(value.get('cartCard').get('name'))).toBeInTheDocument();
        });
      });
    });

    test('deletes card when delete clicked', async () => {
      const cartData = createCartData({ recordCount: 3, cartDataWithQuantities: true });
      const cardDataState = getMockCartStateData({ location: CartLocation.DECKVIEWER, cardDataProps: cartData });
      // Get first card quantity for deletion calculation
      const firstCardQuantity = cardDataState.get('data').first().get('quantity');

      const totalCards = getTotalCards(cardDataState);
      const { container } = renderBuyLinkDialog({
        location: CartLocation.DECKVIEWER,
        me: createUser({ loggedIn: true }),
        cartData,
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
        expect(screen.getByText('Owned')).toBeInTheDocument();
        expect(screen.getByText('In Deck')).toBeInTheDocument();
      });

      // Delete button identified by class name (no role available)
      const deleteButtons = container.querySelectorAll('.cell-border-left.cell-border-right.cell__clickable');
      fireEvent.click(deleteButtons[0]);
      const quantityInput = container.querySelectorAll('input.buy-cards-input[type="number"]');

      // Should be 2 because we deleted one
      expect(quantityInput.length).toBe(2);
      expect(
        screen.getByText(TextFormat.pluralizedCardsCapitalized(totalCards - (firstCardQuantity ?? 0))),
      ).toBeInTheDocument();
    });

    it('shows empty cart message', async () => {
      renderBuyLinkDialog({
        isOpen: true,
        cartData: createCartData({ recordCount: 0 }),
      });

      await waitFor(() => {
        expect(screen.getByText('Click Reset to restore removed cards back to the table.')).toBeInTheDocument();
      });
    });

    describe('with onUpdateMap function', () => {
      // Quantity must be between 0 and 100 - test edge cases
      test('increases quantity within range', async () => {
        const cartData = createCartData({ recordCount: 5 });

        const cartDataState = getMockCartStateData({ cardDataProps: cartData });
        const totalCards = getTotalCards(cartDataState);

        const { container } = renderBuyLinkDialog({
          cartData,
        });

        await waitFor(() => {
          expect(screen.queryByText('Loading')).not.toBeInTheDocument();
        });

        const quantityInput = container.querySelector<HTMLInputElement>('input.buy-cards-input[type="number"]');
        const quantityInputValue = quantityInput?.value;
        expect(quantityInput).toBeInTheDocument();

        // Add current quantity + 5
        fireEvent.change(quantityInput!, { target: { value: (Number(quantityInputValue) ?? 0) + 5 } });
        expect(screen.getByText(TextFormat.pluralizedCardsCapitalized(totalCards + 5))).toBeInTheDocument();
      });

      test('rejects quantity above 100', async () => {
        const cartData = createCartData({ recordCount: 5 });

        const cartDataState = getMockCartStateData({ cardDataProps: cartData });
        const totalCards = getTotalCards(cartDataState);

        const { container } = renderBuyLinkDialog({
          cartData,
        });

        await waitFor(() => {
          expect(screen.queryByText('Loading')).not.toBeInTheDocument();
        });

        const quantityInput = container.querySelector<HTMLInputElement>('input.buy-cards-input[type="number"]');
        const quantityInputValue = quantityInput?.value;
        expect(quantityInput).toBeInTheDocument();

        // Add current quantity + 101 (should be rejected)
        fireEvent.change(quantityInput!, { target: { value: (Number(quantityInputValue) ?? 0) + 101 } });
        expect(screen.getByText(TextFormat.pluralizedCardsCapitalized(totalCards))).toBeInTheDocument();
      });

      test('handles empty quantity input', async () => {
        const cartData = createCartData({ recordCount: 5 });

        const cartDataState = getMockCartStateData({ cardDataProps: cartData });
        const totalCards = getTotalCards(cartDataState);

        const { container } = renderBuyLinkDialog({
          cartData,
        });

        await waitFor(() => {
          expect(screen.queryByText('Loading')).not.toBeInTheDocument();
        });

        const quantityInput = container.querySelector<HTMLInputElement>('input.buy-cards-input[type="number"]');
        const quantityInputValue = quantityInput?.value;
        expect(quantityInput).toBeInTheDocument();

        // Empty quantity sets quantity to undefined - subtract from total
        fireEvent.change(quantityInput!, { target: { value: '' } });
        expect(
          screen.getByText(TextFormat.pluralizedCardsCapitalized(totalCards - (Number(quantityInputValue) ?? 0))),
        ).toBeInTheDocument();
      });
    });
  });

  describe('icon select interactions', () => {
    test('triggers grouping updates', async () => {
      const cartData = createCartData({
        recordCount: 2,
        cartDataWithQuantities: true,
      });

      const { container } = renderBuyLinkDialog({
        isOpen: true,
        location: CartLocation.COLLECTION,
        me: createUser({ loggedIn: true }),
        cartData,
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });
      const selectedService = container.querySelector('.react-select-thin__single-value');

      // Default is Card Kingdom
      expect(selectedService?.textContent).toBe('Card Kingdom');

      fireEvent.mouseDown(screen.getByRole('textbox'));
      fireEvent.click(screen.getByText('TCGplayer'));
      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });
      fireEvent.mouseDown(screen.getByRole('combobox'));
      fireEvent.change(screen.getByRole('combobox'), {
        target: { value: CartTableGrouping.PRINTING },
      });
    });
  });

  describe('form submission', () => {
    const mockSubmit = vi.fn();
    afterEach(() => {
      // Restore the mock to its original implementation
      vi.mocked(CartActions.getCardQuantities).mockResolvedValue(Immutable.Map(mockOwnedCartData));
    });

    it('renders form for external cart submission', async () => {
      const { container } = renderBuyLinkDialog({
        cartData: createCartData({ recordCount: 2, cartDataWithQuantities: true }),
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      // Form has correct submission attributes for external service
      const form = container.querySelector('form');
      expect(form).toHaveAttribute('method', 'post');
      expect(form).toHaveAttribute('target', '_blank');
      expect(form).toHaveAttribute('action');

      // Form includes required hidden data inputs
      const payloadInput = container.querySelector('input[name="c"]');
      const partnerInput = container.querySelector('input[name="partner"]');
      expect(payloadInput).toHaveAttribute('type', 'hidden');
      expect(partnerInput).toHaveAttribute('type', 'hidden');

      // Submit button is enabled when cart has items
      const submitButton = screen.getByRole('button', { name: /send to cart/i });
      expect(submitButton).not.toBeDisabled();
      expect(submitButton).toHaveTextContent('Send to Cart');
    });

    it('disables submission when cart is empty', async () => {
      renderBuyLinkDialog({
        cartData: createCartData({ recordCount: 0 }),
      });

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      const submitButton = screen.getByRole('button', { name: /send to cart/i });
      expect(submitButton).toBeDisabled();
    });

    it('disables submission when all quantities are zero', async () => {
      const { container } = renderBuyLinkDialog();

      await waitFor(() => {
        expect(screen.queryByText('Loading')).not.toBeInTheDocument();
      });

      // Set all quantities to 0
      const quantityInputs = container.querySelectorAll<HTMLInputElement>('input[type="number"]');
      quantityInputs.forEach((input) => {
        fireEvent.change(input, { target: { value: '0' } });
      });

      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: /send to cart/i });
        expect(submitButton).toBeDisabled();
      });
    });

    it('disables submission during loading', async () => {
      // Mock getCardQuantities to return a promise that doesn't resolve immediately
      const slowPromise = new Promise(() => {}); // Never resolves
      vi.mocked(CartActions.getCardQuantities).mockReturnValue(slowPromise as any);

      renderBuyLinkDialog();

      const submitButton = screen.getByRole('button', { name: /send to cart/i });
      expect(submitButton).toBeDisabled();
    });

    describe('with CardKingdom service', () => {
      it('submits form with correct URL and payload', async () => {
        // Mock form submission to prevent actual navigation
        const originalSubmit = HTMLFormElement.prototype.submit;
        HTMLFormElement.prototype.submit = mockSubmit;

        const { container } = renderBuyLinkDialog({
          cartData: createCartData({ recordCount: 2, cartDataWithQuantities: true }),
        });

        await waitFor(() => {
          expect(screen.queryByText('Loading')).not.toBeInTheDocument();
        });

        // Default service should be Card Kingdom
        const selectedService = container.querySelector('.react-select-thin__single-value');
        expect(selectedService?.textContent).toBe(fakeCardKingdomService.type);

        const form = container.querySelector('form');
        const payloadInput = container.querySelector<HTMLInputElement>('input[name="c"]');
        const partnerInput = container.querySelector<HTMLInputElement>('input[name="partner"]');

        // Verify CardKingdom-specific URL and data using fake service
        expect(form).toHaveAttribute('action', fakeCardKingdomService.partnerURL);
        expect(partnerInput?.value).toBe(fakeCardKingdomService.partnerCode);

        // Verify CardKingdom payload format matches fake service pattern
        const payload = payloadInput?.value;
        expect(payload).toBeTruthy();
        // Should follow the same pattern: "quantity cardname||"
        const fakePayload = fakeCardKingdomService.generatePayload(CartTableGrouping.NAME);

        // Pattern explanation: "1 Lightning Bolt||2 Counterspell||3 Black Lotus||"
        // ^ starts with digit(s), space, card name, then repeats with "||" separator, ends with "||"
        const payloadPattern = /^\d+\s.+(\|\|\d+\s.+)*\|\|$/;
        expect(payload).toMatch(payloadPattern);
        expect(fakePayload).toMatch(payloadPattern);

        // Clean up mock
        HTMLFormElement.prototype.submit = originalSubmit;
      });
    });

    describe('with TCGPlayer service', () => {
      it('submits form with correct URL and payload', async () => {
        // Mock form submission to prevent actual navigation
        const originalSubmit = HTMLFormElement.prototype.submit;
        HTMLFormElement.prototype.submit = mockSubmit;

        const { container } = renderBuyLinkDialog({
          cartData: createCartData({ recordCount: 2, cartDataWithQuantities: true }),
        });

        await waitFor(() => {
          expect(screen.queryByText('Loading')).not.toBeInTheDocument();
        });

        // Switch to TCGPlayer service
        fireEvent.mouseDown(screen.getByRole('textbox'));
        fireEvent.click(screen.getByText(fakeTCGPlayerService.type));

        await waitFor(() => {
          const selectedService = container.querySelector('.react-select-thin__single-value');
          expect(selectedService?.textContent).toBe(fakeTCGPlayerService.type);
        });

        const form = container.querySelector('form');
        const payloadInput = container.querySelector<HTMLInputElement>('input[name="c"]');
        const partnerInput = container.querySelector<HTMLInputElement>('input[name="partner"]');

        // Verify TCGPlayer-specific URL and data using fake service
        expect(form).toHaveAttribute('action', fakeTCGPlayerService.partnerURL);
        expect(partnerInput?.value).toBe(fakeTCGPlayerService.partnerCode);

        // Verify TCGPlayer payload format matches fake service pattern
        const payload = payloadInput?.value;
        expect(payload).toBeTruthy();

        // should follow similar patterns with different formats
        const fakePayload = fakeTCGPlayerService.generatePayload(CartTableGrouping.NAME);

        // Pattern explanation: "1 Lightning Bolt||2 Counterspell [M25] 123||3 Black Lotus||"
        // ^ starts with digit(s), space, card name (may include set/collector info), repeats with "||", ends with "||"
        const payloadPattern = /^\d+\s.+(\|\|\d+\s.+)*\|\|$/;
        expect(payload).toMatch(payloadPattern);
        expect(fakePayload).toMatch(payloadPattern);

        // Clean up mock
        HTMLFormElement.prototype.submit = originalSubmit;
      });
    });
  });
});

import { faker } from '@faker-js/faker';
import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create, FakeCartCard, FakeCartCardRecord } from '../../../tests/fake/Fake';
import { CartTableGrouping } from '../../lib/carts/CartService';
import { CartLocation } from '../../models/CartButton';
import { CartRow } from './CartRow';

// Default props for CartRow
const DEFAULT_PROPS = {
  location: CartLocation.COLLECTION,
  grouping: CartTableGrouping.NAME,
  isPublic: false,
  loggedIn: true,
  dark: false,
  final: false,
  onUpdateMap: vi.fn(),
  onDeleteCell: vi.fn(),
  recordList: Immutable.List([create(FakeCartCardRecord)]),
};

const renderCartRow = (props: Partial<React.ComponentProps<typeof CartRow>> = {}) => {
  return render(<CartRow {...DEFAULT_PROPS} {...props} />);
};

describe('CartRow', () => {
  describe('with single record', () => {
    it('renders quantity input with correct value and type', () => {
      const record = create(FakeCartCardRecord, { quantity: 5 });
      const recordList = Immutable.List([record]);
      renderCartRow({ recordList });

      const quantityInput = screen.getByDisplayValue('5');
      expect(quantityInput).toBeInTheDocument();
      expect(quantityInput).toHaveAttribute('type', 'number');
    });

    it('renders card name', () => {
      const cardName = faker.commerce.productName();
      const record = create(FakeCartCardRecord, {
        cartCard: create(FakeCartCard, { name: cardName }),
      });
      const recordList = Immutable.List([record]);
      renderCartRow({ recordList });

      expect(screen.getByText(cardName)).toBeInTheDocument();
    });

    it('renders delete button', () => {
      const recordList = Immutable.List([create(FakeCartCardRecord)]);
      const { container } = renderCartRow({ recordList });

      const deleteButton = container.querySelector('svg');
      expect(deleteButton).toBeInTheDocument();
    });
  });

  describe('with multiple records', () => {
    it('sums quantities correctly', () => {
      const records = [
        create(FakeCartCardRecord, { quantity: 1 }),
        create(FakeCartCardRecord, { quantity: 2 }),
        create(FakeCartCardRecord, { quantity: 3 }),
      ];
      const recordList = Immutable.List(records);
      renderCartRow({ recordList });

      // Should sum quantities: 1 + 2 + 3 = 6
      expect(screen.getByDisplayValue('6')).toBeInTheDocument();
    });

    it('sums owned counts correctly', () => {
      const records = [create(FakeCartCardRecord, { ownedCount: 2 }), create(FakeCartCardRecord, { ownedCount: 4 })];
      const recordList = Immutable.List(records);
      renderCartRow({ recordList, loggedIn: true });

      // Should sum owned counts: 2 + 4 = 6
      expect(screen.getByText('6')).toBeInTheDocument();
    });

    it('sums deck counts correctly', () => {
      const records = [create(FakeCartCardRecord, { deckCount: 1 }), create(FakeCartCardRecord, { deckCount: 2 })];
      const recordList = Immutable.List(records);
      renderCartRow({ recordList, location: CartLocation.DECKVIEWER });

      // Should sum deck counts: 1 + 2 = 3
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('uses only first record card data', () => {
      const firstCardName = 'First Card';
      const secondCardName = 'Second Card';

      const firstRecord = create(FakeCartCardRecord, {
        cartCard: create(FakeCartCard, { name: firstCardName }),
      });
      const secondRecord = create(FakeCartCardRecord, {
        cartCard: create(FakeCartCard, { name: secondCardName }),
      });

      const recordList = Immutable.List([firstRecord, secondRecord]);
      renderCartRow({ recordList });
      // Should use first record's card name
      expect(screen.getByText(firstCardName)).toBeInTheDocument();
      expect(screen.queryByText(secondCardName)).not.toBeInTheDocument();
    });
  });

  describe('with login states', () => {
    it('shows owned count when logged in', () => {
      const record = create(FakeCartCardRecord, { ownedCount: 8 });
      const recordList = Immutable.List([record]);
      renderCartRow({ recordList, loggedIn: true });

      expect(screen.getByText('8')).toBeInTheDocument();
    });

    it('hides owned count when logged out', () => {
      const record = create(FakeCartCardRecord, { ownedCount: 8 });
      const recordList = Immutable.List([record]);
      renderCartRow({ recordList, loggedIn: false });

      // should not show owned count when logged out
      expect(screen.queryByText('8')).not.toBeInTheDocument();
    });
  });

  describe('with locations', () => {
    it('shows deck count in DECKVIEWER location', () => {
      const record = create(FakeCartCardRecord, { deckCount: 4, ownedCount: 7 }); // Use different values
      const recordList = Immutable.List([record]);
      renderCartRow({ recordList, location: CartLocation.DECKVIEWER });

      // Should see both owned count (7) and deck count (4)
      expect(screen.getByText('7')).toBeInTheDocument(); // owned count
      expect(screen.getByText('4')).toBeInTheDocument(); // deck count
    });

    it('hides deck count in COLLECTION location', () => {
      const record = create(FakeCartCardRecord, { deckCount: 99 }); // Use unique value
      const recordList = Immutable.List([record]);
      renderCartRow({ recordList, location: CartLocation.COLLECTION });

      // Should not show deck count text when in COLLECTION location
      expect(screen.queryByText('99')).not.toBeInTheDocument();
    });
  });

  describe('with groupings', () => {
    it('renders with NAME grouping', () => {
      const { container } = renderCartRow({
        grouping: CartTableGrouping.NAME,
      });

      // Verify the component renders without errors
      expect(container.querySelector('.table__cell')).toBeInTheDocument();

      // should not show set symbol when grouping is NAME
      // props.grouping !== CartTableGrouping.NAME
      expect(container.querySelector('.set-symbol__xs')).not.toBeInTheDocument();
    });

    it('renders with SET grouping', () => {
      const { container } = renderCartRow({
        grouping: CartTableGrouping.SET,
      });

      expect(container.querySelector('.table__cell')).toBeInTheDocument();

      // should show set symbol when grouping is SET
      expect(container.querySelector('.set-symbol__xs')).toBeInTheDocument();
    });

    it('renders PRINTING grouping with collector number', () => {
      const collectorNumber = '123';
      const record = create(FakeCartCardRecord, {
        cartCard: create(FakeCartCard, { collectorNumber }),
      });
      const recordList = Immutable.List([record]);
      const { container } = renderCartRow({ recordList, grouping: CartTableGrouping.PRINTING });

      expect(screen.getByText(collectorNumber)).toBeInTheDocument();
      // should show set symbol when grouping is PRINTING
      expect(container.querySelector('.set-symbol__xs')).toBeInTheDocument();
    });
  });

  describe('with styling props', () => {
    it('applies dark styling', () => {
      const recordList = Immutable.List([create(FakeCartCardRecord)]);
      const { container } = renderCartRow({ recordList, dark: true });

      const darkCells = container.querySelectorAll('.cell__dark');
      expect(darkCells.length).toBeGreaterThan(0);
    });

    it('applies final row styling', () => {
      const recordList = Immutable.List([create(FakeCartCardRecord)]);
      const { container } = renderCartRow({ recordList, final: true });

      // should show bottom border when final is true
      const bottomBorderCells = container.querySelectorAll('.cell-border-bottom');
      expect(bottomBorderCells.length).toBeGreaterThan(0);
    });
  });

  describe('with user interactions', () => {
    it('triggers onUpdateMap prop', () => {
      const onUpdateMap = vi.fn();
      const record = create(FakeCartCardRecord, { quantity: 3 });
      const recordList = Immutable.List([record]);
      renderCartRow({ recordList, onUpdateMap });

      const quantityInput = screen.getByDisplayValue('3');
      fireEvent.change(quantityInput, { target: { value: '5' } });

      expect(onUpdateMap).toHaveBeenCalledWith(record.get('cartCard').get('jsonID'), '5');
    });

    it('triggers onDeleteCell prop', () => {
      const onDeleteCell = vi.fn();
      const record = create(FakeCartCardRecord);
      const recordList = Immutable.List([record]);
      const { container } = renderCartRow({ recordList, onDeleteCell });

      const deleteButton = container.querySelector('.cell__clickable');
      expect(deleteButton).toBeInTheDocument();

      fireEvent.click(deleteButton!);

      expect(onDeleteCell).toHaveBeenCalledWith(record.get('cartCard').get('jsonID'));
    });
  });

  it('handles undefined quantity', () => {
    // Create a record with undefined quantity by setting it to undefined after creation
    const baseRecord = create(FakeCartCardRecord);
    const record = baseRecord.set('quantity', undefined);
    const recordList = Immutable.List([record]);
    renderCartRow({ recordList });

    // The component treats undefined quantities as 0 during reduction
    expect(screen.getByDisplayValue('0')).toBeInTheDocument();
  });

  it('handles zero values', () => {
    const record = create(FakeCartCardRecord, {
      quantity: 0,
      ownedCount: 0,
      deckCount: 0,
    });
    const recordList = Immutable.List([record]);
    renderCartRow({
      recordList,
      location: CartLocation.DECKVIEWER,
    });

    expect(screen.getByDisplayValue('0')).toBeInTheDocument();
    expect(screen.getAllByText('0')).toHaveLength(2); // owned count and deck count
  });

  it('handles empty set information', () => {
    const cartCard = create(FakeCartCard).asMutable();
    cartCard.set('setName', undefined);
    cartCard.set('setCode', undefined);
    cartCard.set('collectorNumber', undefined);
    const record = create(FakeCartCardRecord, {
      cartCard,
    });

    const recordList = Immutable.List([record]);
    const { container } = renderCartRow({
      recordList,
      grouping: CartTableGrouping.PRINTING,
    });

    // Should still render without errors
    expect(container.querySelector('.table__cell')).toBeInTheDocument();
  });
});

import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it } from 'vitest';
import { FormLabel } from './FormLabel';

const DEFAULT_PROPS = {
  heading: 'Test Heading',
  subheading: 'Test Subheading',
  addMargin: true,
};
type FormLabelProps = ComponentProps<typeof FormLabel>;
const renderFormLabel = (props: Partial<FormLabelProps> = {}) => {
  return render(<FormLabel {...DEFAULT_PROPS} {...props} />);
};

describe('FormLabel', () => {
  describe('when rendered', () => {
    it('renders heading when provided', () => {
      renderFormLabel({ heading: DEFAULT_PROPS.heading });
      expect(screen.getByText(DEFAULT_PROPS.heading)).toBeInTheDocument();
      expect(screen.getByText(DEFAULT_PROPS.heading)).toHaveClass('form-label--heading');
    });

    it('renders subheading when provided', () => {
      renderFormLabel({ subheading: DEFAULT_PROPS.subheading });
      expect(screen.getByText(DEFAULT_PROPS.subheading)).toBeInTheDocument();
    });

    it('does not render heading when not provided', () => {
      const { container } = renderFormLabel({ heading: undefined });
      expect(container.querySelector('form-label--heading')).not.toBeInTheDocument();
    });

    it('does not render subheading when not provided', () => {
      const { container } = renderFormLabel({ subheading: undefined });
      expect(container.children).toHaveLength(1);
    });
  });

  // TODO: This should be moved to css when migrated
  describe('margin behavior', () => {
    it('applies margins when addMargin prop is true', () => {
      const { container } = renderFormLabel();
      const formLabel = container.querySelector('.form-label');
      expect(formLabel).toHaveStyle({
        marginBottom: '1rem',
        marginTop: '0rem',
      });
    });

    it('applies no margins when addMargin prop is false', () => {
      const { container } = renderFormLabel({ addMargin: false });
      const headingElement = container.querySelector('.form-label');
      expect(headingElement).toHaveStyle({
        marginBottom: '0rem',
      });
    });

    it('applies correct margins with only heading', () => {
      const { container } = renderFormLabel({ subheading: undefined });
      const formLabel = container.querySelector('.form-label');
      expect(formLabel).toHaveStyle({
        marginBottom: '0.5rem',
        marginTop: '0rem',
      });
    });

    it('applies correct margins with only subheading', () => {
      const { container } = renderFormLabel({ heading: undefined });
      const formLabel = container.querySelector('.form-label');
      expect(formLabel).toHaveStyle({
        marginBottom: '0rem',
        marginTop: '0.5rem',
      });
    });
  });
});

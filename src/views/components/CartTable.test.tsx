import { render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import { create, FakeCartData } from '../../../tests/fake/Fake';
import { CartTableGrouping } from '../../lib/carts/CartService';
import { CartLocation } from '../../models/CartButton';
import { CartData } from '../../models/CartCards';
import { CartTable } from './CartTable';

// Default props for CartTable
const DEFAULT_PROPS = {
  location: CartLocation.COLLECTION,
  grouping: CartTableGrouping.NAME,
  isPublic: false,
  loggedIn: true,
  loading: false,
  onUpdateMap: () => {},
  onDeleteCell: () => {},
};

const renderCartTable = (props: Partial<React.ComponentProps<typeof CartTable>> = {}) => {
  return render(<CartTable {...DEFAULT_PROPS} {...props} />);
};

describe('CartTable', () => {
  describe('when loading', () => {
    it('shows loading state', () => {
      const { container } = renderCartTable({ loading: true });

      expect(screen.getByText('Loading')).toBeInTheDocument();
      expect(container.querySelector('.spinner-xs')).toBeInTheDocument();
    });

    it('shows loading when cartData undefined', () => {
      renderCartTable({ cartData: undefined });

      expect(screen.getByText('Loading')).toBeInTheDocument();
    });
  });

  describe('when empty', () => {
    it('shows empty state message', () => {
      const emptyCartData = new CartData({ data: Immutable.Map() });
      renderCartTable({ cartData: emptyCartData });

      expect(screen.getByText('Click Reset to restore removed cards back to the table.')).toBeInTheDocument();
    });
  });

  describe('with cart data', () => {
    it('renders table with data', () => {
      const cartData = create(FakeCartData, { recordCount: 2 });
      const { container } = renderCartTable({ cartData });

      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Card Name')).toBeInTheDocument();
      expect(container.querySelector('.base-table')).toBeInTheDocument();
    });
  });

  describe('with groupings', () => {
    it('renders NAME grouping headers', () => {
      const cartData = create(FakeCartData, { recordCount: 1 });
      renderCartTable({
        cartData,
        loggedIn: true,
        location: CartLocation.COLLECTION,
        grouping: CartTableGrouping.NAME,
      });

      // Always present headers
      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Card Name')).toBeInTheDocument();
      expect(screen.getByText('Owned')).toBeInTheDocument();

      // Should not show grouping-specific headers
      expect(screen.queryByText('Set')).not.toBeInTheDocument();
      expect(screen.queryByText('#')).not.toBeInTheDocument();
    });

    it('renders SET grouping headers', () => {
      const cartData = create(FakeCartData, { recordCount: 1 });
      renderCartTable({
        cartData,
        loggedIn: true,
        location: CartLocation.COLLECTION,
        grouping: CartTableGrouping.SET,
      });

      // Always present headers
      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Card Name')).toBeInTheDocument();
      expect(screen.getByText('Owned')).toBeInTheDocument();

      // Should show Set header but not collector number
      expect(screen.getByText('Set')).toBeInTheDocument();
      expect(screen.queryByText('#')).not.toBeInTheDocument();
    });

    it('renders PRINTING grouping headers', () => {
      const cartData = create(FakeCartData, { recordCount: 1 });
      renderCartTable({
        cartData,
        loggedIn: true,
        location: CartLocation.COLLECTION,
        grouping: CartTableGrouping.PRINTING,
      });

      // Always present headers
      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Card Name')).toBeInTheDocument();
      expect(screen.getByText('Owned')).toBeInTheDocument();

      // Should show both Set and collector number headers
      expect(screen.getByText('Set')).toBeInTheDocument();
      expect(screen.getByText('#')).toBeInTheDocument();
    });
  });

  describe('with locations', () => {
    it('renders COLLECTION location headers', () => {
      const cartData = create(FakeCartData, { recordCount: 1 });
      renderCartTable({
        cartData,
        loggedIn: true,
        location: CartLocation.COLLECTION,
        grouping: CartTableGrouping.NAME,
      });

      // Always present headers
      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Card Name')).toBeInTheDocument();
      expect(screen.getByText('Owned')).toBeInTheDocument();

      // Should not show deck-specific header
      expect(screen.queryByText('In Deck')).not.toBeInTheDocument();
    });

    it('renders DECKVIEWER location headers', () => {
      const cartData = create(FakeCartData, { recordCount: 1 });
      renderCartTable({
        cartData,
        location: CartLocation.DECKVIEWER,
        grouping: CartTableGrouping.NAME,
      });

      // Always present headers
      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Card Name')).toBeInTheDocument();
      expect(screen.getByText('Owned')).toBeInTheDocument();

      // Should show deck-specific header
      expect(screen.getByText('In Deck')).toBeInTheDocument();
    });
  });

  describe('when logged in', () => {
    describe('with DECKVIEWER location', () => {
      it('show correct headers', () => {
        const cartData = create(FakeCartData, { recordCount: 1 });
        renderCartTable({
          cartData,
          loggedIn: true,
          location: CartLocation.DECKVIEWER,
          grouping: CartTableGrouping.PRINTING,
        });

        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Owned')).toBeInTheDocument();
        expect(screen.getByText('In Deck')).toBeInTheDocument();
        expect(screen.getByText('Set')).toBeInTheDocument();
        expect(screen.getByText('#')).toBeInTheDocument();
        expect(screen.getByText('Card Name')).toBeInTheDocument();
      });
    });
    describe('with COLLECTION location', () => {
      it('show correct headers', () => {
        const cartData = create(FakeCartData, { recordCount: 1 });
        renderCartTable({
          cartData,
          loggedIn: true,
          location: CartLocation.COLLECTION,
          grouping: CartTableGrouping.NAME,
        });

        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Owned')).toBeInTheDocument();
        expect(screen.getByText('Card Name')).toBeInTheDocument();
      });
    });
  });

  describe('when logged out', () => {
    describe('with DECKVIEWER location', () => {
      it('show correct headers', () => {
        const cartData = create(FakeCartData, { recordCount: 1 });
        renderCartTable({
          cartData,
          loggedIn: false,
          location: CartLocation.DECKVIEWER,
          grouping: CartTableGrouping.NAME,
        });

        // Should have only basic headers
        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Card Name')).toBeInTheDocument();
        expect(screen.getByText('In Deck')).toBeInTheDocument();

        expect(screen.queryByText('Owned')).not.toBeInTheDocument();
        expect(screen.queryByText('Set')).not.toBeInTheDocument();
        expect(screen.queryByText('#')).not.toBeInTheDocument();
      });
    });
    describe('with COLLECTION location', () => {
      it('show correct headers', () => {
        const cartData = create(FakeCartData, { recordCount: 1 });
        renderCartTable({
          cartData,
          loggedIn: false,
          location: CartLocation.COLLECTION,
        });

        // Should have only basic headers
        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Card Name')).toBeInTheDocument();

        expect(screen.queryByText('Owned')).not.toBeInTheDocument();
      });
    });
  });

  describe('with CartRow', () => {
    it('renders CartRow components', () => {
      const countData = 3;
      const cartData = create(FakeCartData, { recordCount: countData });
      const { container } = renderCartTable({ cartData });

      // Should render multiple cart rows based on grouped data
      const tableElement = container.querySelector('.base-table');
      expect(tableElement).toBeInTheDocument();

      // Verify table structure exists
      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Card Name')).toBeInTheDocument();

      expect(container.querySelectorAll('.buy-cards-input')).toHaveLength(countData);
    });
  });
});

import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { Placeholder } from './Placeholder';

// Mock the history helper
vi.mock('../../helpers/history', () => ({
  default: {
    push: vi.fn(),
  },
}));

import history from '../../helpers/history';

const mockOnClick = vi.fn();
const mockHistoryPush = vi.mocked(history.push);

type PlaceholderProps = ComponentProps<typeof Placeholder>;

const DEFAULT_PROPS: PlaceholderProps = {
  message: 'Test message',
  button: null,
};

const renderPlaceholder = (props: Partial<PlaceholderProps> = {}) => {
  return render(<Placeholder {...DEFAULT_PROPS} {...props} />);
};

describe('Placeholder', () => {
  it('displays message', () => {
    renderPlaceholder({ message: 'Custom message' });
    expect(screen.getByText('Custom message')).toBeInTheDocument();
  });

  describe('when button prop is null or falsy', () => {
    it('does not render button element', () => {
      renderPlaceholder({ button: null });
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    test('treats empty string as falsy', () => {
      const { container } = renderPlaceholder({ button: '' });
      expect(container.querySelector('.placeholder-button')).not.toBeInTheDocument();
    });
  });

  describe('when button prop is provided', () => {
    // NOTE: The "button" is currently implemented as a div element, not a proper button
    // This should be migrated to use a proper <button> element for better accessibility
    it('renders button element', () => {
      renderPlaceholder({ button: 'Click me' });
      expect(screen.getByText('Click me')).toBeInTheDocument();
    });

    describe('with onClick prop', () => {
      test('calls onClick prop', () => {
        renderPlaceholder({ button: 'Click me', onClick: mockOnClick });

        fireEvent.click(screen.getByText('Click me'));
        expect(mockOnClick).toHaveBeenCalledTimes(1);
      });

      test('prioritizes onClick prop over href', () => {
        renderPlaceholder({ button: 'Click me', onClick: mockOnClick, href: '/test-path' });

        fireEvent.click(screen.getByText('Click me'));
        expect(mockOnClick).toHaveBeenCalled();
        expect(mockHistoryPush).not.toHaveBeenCalled();
      });
    });

    describe('with href prop only', () => {
      test('navigates to href when clicked', () => {
        renderPlaceholder({ button: 'Navigate', href: '/test-path' });

        fireEvent.click(screen.getByText('Navigate'));
        expect(mockHistoryPush).toHaveBeenCalledWith('/test-path');
      });
    });

    describe('without onClick or href', () => {
      test('does nothing when clicked', () => {
        renderPlaceholder({ button: 'Do nothing' });

        fireEvent.click(screen.getByText('Do nothing'));
        expect(mockOnClick).not.toHaveBeenCalled();
        expect(mockHistoryPush).not.toHaveBeenCalled();
      });
    });
  });
});

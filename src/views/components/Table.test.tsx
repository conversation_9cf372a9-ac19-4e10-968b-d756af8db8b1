import { describe, expect, it, vi } from 'vitest';
import { CellBorders } from '../../models/CellBorder';
import { ColumnSize, generateCellClasses, generateInnerClasses, generateSorting, TableConfiguration } from './Table';

const mockOnClick = vi.fn();
export const DEFAULT_PROPS = {
  dark: false,
  onClick: mockOnClick,
  cellBorders: new CellBorders(),
};

describe('Table Components', () => {
  describe('generateCellClasses utility function', () => {
    it('applies dark classes when dark prop is true', () => {
      const cellProps = {
        ...DEFAULT_PROPS,
        dark: true,
      };
      const classes = generateCellClasses(cellProps);
      expect(classes).toContain('cell__dark');
    });
    it('applies correct collection cell classes when collectionCell is true', () => {
      const cellProps = {
        ...DEFAULT_PROPS,
        collectionCell: true,
      };
      const classes = generateCellClasses(cellProps);
      expect(classes).toContain('collection-cell-justification');
    });
    it('applies correct clickable classes when onClick prop is provided', () => {
      const cellProps = {
        ...DEFAULT_PROPS,
        onclick: mockOnClick,
      };
      const classes = generateCellClasses(cellProps);
      expect(classes).toContain('cell__clickable');
    });
    it('applies correct large classes when large is true', () => {
      const cellProps = {
        ...DEFAULT_PROPS,
        large: true,
      };
      const classes = generateCellClasses(cellProps);
      expect(classes).toContain('cell__large');
    });
    it('applies correct border cell classes when cellBorders is provided', () => {
      const cellProps = {
        ...DEFAULT_PROPS,
        cellBorders: new CellBorders({ top: true, left: true, right: true, bottom: true }),
      };
      const classes = generateCellClasses(cellProps);
      expect(classes).toContain('cell-border-top cell-border-left cell-border-right cell-border-bottom');
    });

    it('applies correct cell classes with combined props', () => {
      const cellProps = {
        ...DEFAULT_PROPS,
        dark: true,
        large: true,
        collectionCell: true,
        cellBorders: new CellBorders({ top: true, bottom: true }),
      };
      const classes = generateCellClasses(cellProps);
      expect(classes).toBe(
        'table__cell collection-cell-justification cell__clickable cell__dark cell__large cell-border-top cell-border-bottom',
      );
    });
  });
  describe('generateInnerClasses utility function', () => {
    it('applies correct centerCell classes when centerCell is true', () => {
      const cellProps = {
        ...DEFAULT_PROPS,
        centerCell: true,
      };
      const classes = generateInnerClasses(cellProps);
      expect(classes).toContain('cell-center');
    });

    it('applies correct cell classes with combined props', () => {
      // When collectionCell and centerCell both true
      const cellPropsCollectionAndCenter = {
        ...DEFAULT_PROPS,
        collectionCell: true,
        centerCell: true,
      };
      const classesCollectionAndCenter = generateInnerClasses(cellPropsCollectionAndCenter);
      expect(classesCollectionAndCenter).toContain('collection-cell-inner');
      expect(classesCollectionAndCenter).toContain('cell-center');
      expect(classesCollectionAndCenter).not.toContain('card-name-cell-text');

      // When collectionCell and cardNameFormatting both true (mutually exclusive)
      const cellPropsCollectionAndCardName = {
        ...DEFAULT_PROPS,
        collectionCell: true,
        cardNameFormatting: true,
      };
      const classesCollectionAndCardName = generateInnerClasses(cellPropsCollectionAndCardName);
      expect(classesCollectionAndCardName).not.toContain('collection-cell-inner');
      expect(classesCollectionAndCardName).toContain('card-name-cell-text');

      // When centerCell and cardNameFormatting both true
      const cellPropsCenterAndCardName = {
        ...DEFAULT_PROPS,
        centerCell: true,
        cardNameFormatting: true,
      };
      const classesCenterAndCardName = generateInnerClasses(cellPropsCenterAndCardName);
      expect(classesCenterAndCardName).toContain('cell-center');
      expect(classesCenterAndCardName).toContain('card-name-cell-text');
      expect(classesCenterAndCardName).not.toContain('collection-cell-inner');

      // When All three props true
      const cellPropsAllThree = {
        ...DEFAULT_PROPS,
        collectionCell: true,
        centerCell: true,
        cardNameFormatting: true,
      };
      const classesAllThree = generateInnerClasses(cellPropsAllThree);
      expect(classesAllThree).toContain('cell-center');
      expect(classesAllThree).toContain('card-name-cell-text');
      expect(classesAllThree).not.toContain('collection-cell-inner');
    });
  });
  describe('TableConfiguration', () => {
    it('creates correct grid template columns style', () => {
      const config = new TableConfiguration([ColumnSize.MAX_CONTENT, ColumnSize.MINMAX_100]);
      expect(config.style().gridTemplateColumns).toBe(' max-content minmax(max-content, 100%)');
    });

    it('generates correct repeat pattern', () => {
      const pattern = TableConfiguration.repeat(3, ColumnSize.MAX_CONTENT);
      expect(pattern).toBe('repeat(3, max-content)');
    });
  });

  describe('generateSorting utility function', () => {
    describe('when defaultOrder is "asc"', () => {
      it('toggles from asc to desc when sorting is asc', () => {
        const result = generateSorting('name_asc', 'name_asc', 'name_desc', 'asc');
        expect(result).toBe('name_desc');
      });
      it('sets to asc when sorting is not asc', () => {
        const resultForDescSortingType = generateSorting('name_desc', 'name_asc', 'name_desc', 'asc');
        expect(resultForDescSortingType).toBe('name_asc');

        const resultForAnySortingType = generateSorting('anysortType', 'name_asc', 'name_desc', 'asc');
        expect(resultForAnySortingType).toBe('name_asc');
      });
    });
    describe('when defaultOrder is "desc"', () => {
      it('toggles from desc to asc when sorting is desc', () => {
        const result = generateSorting('name_desc', 'name_asc', 'name_desc', 'desc');
        expect(result).toBe('name_asc');
      });
      it('sets to desc when sorting is not desc', () => {
        const resultForAscSortingType = generateSorting('name_asc', 'name_asc', 'name_desc', 'desc');
        expect(resultForAscSortingType).toBe('name_desc');

        const resultForAnySortingType = generateSorting('anysortType', 'name_asc', 'name_desc', 'desc');
        expect(resultForAnySortingType).toBe('name_desc');
      });
    });
  });
});

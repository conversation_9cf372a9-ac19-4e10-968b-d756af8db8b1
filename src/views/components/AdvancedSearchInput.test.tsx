import { createEvent, fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import * as superagent from 'superagent';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import 'vitest-dom/extend-expect';
import { createArray } from '../../../tests/fake/Fake';
import { FakeCardSet } from '../../../tests/fake/FakeCardSet';
import { renderWithDispatcher } from '../../../tests/test-utils';
import * as FilterActions from '../../actions/FilterActions';
import * as CardSetsApi from '../../api/CardSets';
import { MTGFilter } from '../../models/filters/mtg/MTGFilters';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { Tag } from '../../models/Tags';
import { AdvancedSearchInput } from './AdvancedSearchInput';
const DEFAULT_PROPS: Omit<React.ComponentProps<typeof AdvancedSearchInput>, 'dispatcher'> = {
  cardPage: new MTGCardPage(),
  rawQuery: '',
  filterOptions: new MTGFilter(),
};
const mockCardSetData = createArray(FakeCardSet, 10);
const mockRequestGetTags = (tags: Tag[] | Record<string, any>, isRejected = false) => {
  return vi.spyOn(superagent, 'get').mockReturnValue({
    set: vi.fn().mockReturnThis(),
    end: vi.fn().mockImplementation((onFulfilled) => {
      if (!isRejected) {
        onFulfilled(null, {
          body: {
            tags,
          },
        });
      } else {
        onFulfilled({ text: 'something went wrong' });
      }
    }),
  } as any);
};
const typeInSearch = (rawQuery: string) => {
  const input = screen.getByRole('textbox');
  // Search suggestions only show when input is focused,
  // so we need to fire focus event before change event
  fireEvent.focus(input);
  fireEvent.change(input, { target: { value: rawQuery } });
  return input;
};
const renderAdvancedSearchInput = (props: Partial<React.ComponentProps<typeof AdvancedSearchInput>> = {}) => {
  return renderWithDispatcher(AdvancedSearchInput, { ...DEFAULT_PROPS, ...props });
};
describe('AdvancedSearchInput', () => {
  beforeEach(() => {
    vi.spyOn(FilterActions, 'updateQuery');
    vi.spyOn(FilterActions, 'activateFilter');
    vi.spyOn(CardSetsApi, 'search').mockResolvedValue(Immutable.List(mockCardSetData));
  });

  describe('when rendered', () => {
    it('basic render', () => {
      renderAdvancedSearchInput();
      expect(screen.getByText('Search')).toBeInTheDocument();
      expect(screen.getByText('Clear')).toBeInTheDocument();
      expect(screen.getByText('Search your collection')).toBeInTheDocument();
    });

    it('renders input with provided rawQuery', () => {
      const rawQuery = `s:test`;
      renderAdvancedSearchInput({ rawQuery: `s:test` });
      expect(screen.getByDisplayValue(rawQuery)).toBeInTheDocument();
    });
    it('renders input with empty value when no rawQuery provided', () => {
      renderAdvancedSearchInput();
      expect(screen.getByDisplayValue('')).toBeInTheDocument();
    });
  });
  describe('search suggestions', () => {
    describe('when input rawQuery matchTags', () => {
      it('shows suggestions when tags are available', async () => {
        const mock = mockRequestGetTags([new Tag({ name: 'test-tag-1' }), new Tag({ name: 'test-tag-2' })]);
        renderAdvancedSearchInput();
        const tagName = 'test-tag-1';
        const rawQuery = `#:${tagName}`;
        typeInSearch(rawQuery);
        await waitFor(() => {
          expect(mock).toBeCalledWith(`/api/v3/tags?query=${tagName}`);
        });
        expect(screen.getByText(`test-tag-1`, { selector: '.advanced-search-input__suggestion' })).toBeInTheDocument();
      });
      it('hides suggestions when tags are not available', async () => {
        const mock = mockRequestGetTags([]);
        const { container } = renderAdvancedSearchInput();
        const tagName = 'test-tag-1';
        const rawQuery = `#:${tagName}`;
        typeInSearch(rawQuery);
        await waitFor(() => {
          expect(mock).toBeCalledWith(`/api/v3/tags?query=${tagName}`);
        });
        expect(container.querySelector('.advanced-search-input__suggestions')).not.toBeInTheDocument();
      });
      it('hides suggestions when get tags throw error', async () => {
        const mock = mockRequestGetTags([new Tag({ name: 'test-tag-1' })], true);
        const { container } = renderAdvancedSearchInput();
        const tagName = 'test-tag-1';
        const rawQuery = `#:${tagName}`;
        typeInSearch(rawQuery);
        await waitFor(() => {
          expect(mock).toBeCalledWith(`/api/v3/tags?query=${tagName}`);
        });
        expect(container.querySelector('.advanced-search-input__suggestions')).not.toBeInTheDocument();
      });
    });
    describe('when input rawQuery matchSets', () => {
      it('shows suggestions when cardset are available', async () => {
        const { container } = renderAdvancedSearchInput();
        const cardName = mockCardSetData[0].get('name');
        const rawQuery = `s:${cardName}`;
        typeInSearch(rawQuery);
        await waitFor(() => {
          expect(CardSetsApi.search).toBeCalledWith(cardName);
        });
        expect(container.querySelector('.advanced-search-input__suggestions')).toBeInTheDocument();
        mockCardSetData.forEach((cardSet) => {
          expect(screen.getByText(cardSet.get('name'))).toBeInTheDocument();
        });
      });

      it('hides suggestions when cardset are not available', async () => {
        vi.spyOn(CardSetsApi, 'search').mockResolvedValue(Immutable.List([]));
        const { container } = renderAdvancedSearchInput();
        const cardName = 'cardtest';
        const rawQuery = `s:${cardName}`;
        typeInSearch(rawQuery);
        await waitFor(() => {
          expect(CardSetsApi.search).toBeCalledWith(cardName);
        });
        expect(container.querySelector('.advanced-search-input__suggestions')).not.toBeInTheDocument();
      });
      it('hides suggestions when get cardset throw error', async () => {
        vi.spyOn(CardSetsApi, 'search').mockRejectedValue({ res: { text: 'something went wrong' } });
        const { container } = renderAdvancedSearchInput();
        const cardName = 'cardtest';
        const rawQuery = `s:${cardName}`;
        typeInSearch(rawQuery);
        await waitFor(() => {
          expect(CardSetsApi.search).toBeCalledWith(cardName);
        });
        expect(container.querySelector('.advanced-search-input__suggestions')).not.toBeInTheDocument();
      });
    });
    describe('when input rawQuery matchColor', () => {
      it('shows suggestions when colors are available', () => {
        const { container } = renderAdvancedSearchInput();
        const colorName = 'red';
        const rawQuery = `c:${colorName}`;
        typeInSearch(rawQuery);
        expect(container.querySelector('.advanced-search-input__suggestions')).toBeInTheDocument();
        expect(screen.getByText(colorName, { selector: '.advanced-search-input__suggestion' })).toBeInTheDocument();
      });

      it('hides suggestions when colors are not available', () => {
        const { container } = renderAdvancedSearchInput();
        const colorName = 'invalidColor';
        const rawQuery = `c:${colorName}`;
        typeInSearch(rawQuery);
        expect(container.querySelector('.advanced-search-input__suggestions')).not.toBeInTheDocument();
        expect(
          screen.queryByText(colorName, { selector: '.advanced-search-input__suggestion' }),
        ).not.toBeInTheDocument();
      });
    });
    describe('when input rawQuery matchFoils', () => {
      it('shows suggestion when foils are available', () => {
        const { container } = renderAdvancedSearchInput();
        const foilName = 'true';
        const rawQuery = `f:${foilName}`;
        typeInSearch(rawQuery);
        expect(container.querySelector('.advanced-search-input__suggestions')).toBeInTheDocument();
        expect(screen.getByText(foilName, { selector: '.advanced-search-input__suggestion' })).toBeInTheDocument();
      });
      it('hides suggestion when foils not available', () => {
        const { container } = renderAdvancedSearchInput();
        const foilName = 'invalidFoild';
        const rawQuery = `f:${foilName}`;
        typeInSearch(rawQuery);
        expect(container.querySelector('.advanced-search-input__suggestions')).not.toBeInTheDocument();
        expect(
          screen.queryByText(foilName, { selector: '.advanced-search-input__suggestion' }),
        ).not.toBeInTheDocument();
      });
    });
  });
  describe('interactions', () => {
    it('updates input value on change', () => {
      const { dispatcher } = renderAdvancedSearchInput();
      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: 'test-query' } });
      expect(FilterActions.updateQuery).toHaveBeenCalledWith('test-query', dispatcher);
    });

    it('clears input when clear button clicked', () => {
      const { dispatcher } = renderAdvancedSearchInput({ rawQuery: 'existing search' });
      const clearButton = screen.getByText('Clear');
      fireEvent.click(clearButton);
      expect(FilterActions.updateQuery).toHaveBeenCalledWith('', dispatcher);
    });

    it('shows focus state when input is focused', () => {
      const { container } = renderAdvancedSearchInput();
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      expect(container.querySelector('.advanced-search-input.is-focus')).toBeInTheDocument();
    });

    it('selects suggestion when clicked', async () => {
      const { container, dispatcher, rerenderWithDispatcher } = renderAdvancedSearchInput();
      const cardName = 'cardtest';
      const rawQuery = `s:${cardName}`;
      typeInSearch(rawQuery);
      expect(FilterActions.updateQuery).toHaveBeenCalledWith(rawQuery, dispatcher);

      // Suggestion will be selected only when the `rawQuery` prop is valid according to `FilterRegex`,
      // so we need to rerender the component and update the `rawQuery` prop with a new valid value.
      rerenderWithDispatcher({ rawQuery });

      await waitFor(() => {
        expect(CardSetsApi.search).toBeCalledWith(cardName);
      });
      expect(container.querySelector('.advanced-search-input__suggestions')).toBeInTheDocument();

      const selectedCardName = mockCardSetData[0].get('name');
      const suggestion = screen.getByText(mockCardSetData[0].get('name'), {
        selector: '.advanced-search-input__suggestion',
      });

      fireEvent.click(suggestion);
      expect(FilterActions.updateQuery).toHaveBeenCalledWith(`s:${selectedCardName}`, dispatcher);
    });
    it('calls onSubmit on form submission', () => {
      const { container } = renderAdvancedSearchInput();
      const form = container.querySelector('form') as HTMLFormElement;
      fireEvent.submit(form);
      expect(FilterActions.activateFilter).toBeCalled();
    });
    it('prevents default when click form', () => {
      const { container } = renderAdvancedSearchInput();
      const form = container.querySelector('form') as HTMLFormElement;
      const submitEvent = createEvent.click(form);
      const preventDefaultSpy = vi.spyOn(submitEvent, 'preventDefault');
      fireEvent(form, submitEvent);
      expect(preventDefaultSpy).toHaveBeenCalled();
    });
    it('navigates suggestions with arrow keys', async () => {
      const { container } = renderAdvancedSearchInput();
      const cardName = 'cardtest';
      const rawQuery = `s:${cardName}`;
      const input = typeInSearch(rawQuery);
      await waitFor(() => {
        expect(CardSetsApi.search).toBeCalledWith(cardName);
      });
      expect(container.querySelector('.advanced-search-input__suggestions')).toBeInTheDocument();

      // This will navigate to first suggestion
      fireEvent.keyDown(input, { keyCode: 40 });
      const suggestion = screen.getByText(mockCardSetData[0].get('name'), {
        selector: '.advanced-search-input__suggestion',
      });
      expect(suggestion).toHaveClass('is-selected');

      // This will navigate to second suggestion
      fireEvent.keyDown(input, { keyCode: 40 });
      const suggestion2 = screen.getByText(mockCardSetData[1].get('name'), {
        selector: '.advanced-search-input__suggestion',
      });
      expect(suggestion2).toHaveClass('is-selected');

      // This will navigate back to fisrt suggestion
      fireEvent.keyDown(input, { keyCode: 38 });
      expect(suggestion).toHaveClass('is-selected');
    });
    it('selects suggestion using enter key', async () => {
      const { container, dispatcher, rerenderWithDispatcher } = renderAdvancedSearchInput();
      const cardName = 'cardtest';
      const rawQuery = `s:${cardName}`;
      const input = typeInSearch(rawQuery);
      expect(FilterActions.updateQuery).toHaveBeenCalledWith(rawQuery, dispatcher);

      // Suggestion will be selected only when the `rawQuery` prop is valid according to `FilterRegex`,
      // so we need to rerender the component and update the `rawQuery` prop with a new valid value.
      rerenderWithDispatcher({ rawQuery });

      await waitFor(() => {
        expect(CardSetsApi.search).toBeCalledWith(cardName);
      });
      expect(container.querySelector('.advanced-search-input__suggestions')).toBeInTheDocument();

      // This will navigate to first suggestion
      fireEvent.keyDown(input, { keyCode: 40 });
      const selectedCardName = mockCardSetData[0].get('name');
      const suggestion = screen.getByText(selectedCardName, {
        selector: '.advanced-search-input__suggestion',
      });
      expect(suggestion).toHaveClass('is-selected');
      (FilterActions.updateQuery as any).mockClear();
      // Press enter to select
      fireEvent.keyDown(input, { keyCode: 13 });

      // When selecting suggestion with enter key, it adds a space after the query
      expect(FilterActions.updateQuery).toHaveBeenCalledWith(`s:${selectedCardName} `, dispatcher);
    });
  });
});

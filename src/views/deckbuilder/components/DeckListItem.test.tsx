import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { CellBorders } from '../../../models/CellBorder';
import { DeckColorCell } from './DeckListItem';

type DeckColorCellProps = React.ComponentProps<typeof DeckColorCell>;

const createMockCellBorders = (overrides: Partial<Record<string, boolean>> = {}): CellBorders => {
  return new CellBorders({
    top: false,
    left: false,
    right: false,
    bottom: false,
    ...overrides,
  });
};

const defaultProps: DeckColorCellProps = {
  deck: create(FakeDeck, {
    colorWhite: false,
    colorBlue: false,
    colorBlack: false,
    colorRed: false,
    colorGreen: false,
  }),
  dark: false,
  onClick: vi.fn(),
  cellBorders: createMockCellBorders(),
};

const renderDeckColorCell = (props: Partial<DeckColorCellProps> = {}) => {
  return render(<DeckColorCell {...defaultProps} {...props} />);
};

describe('DeckColorCell', () => {
  describe('when component renders', () => {
    it('displays the cell container', () => {
      const { container } = renderDeckColorCell();
      expect(container.querySelector('.table__cell')).toBeInTheDocument();
    });

    it('applies correct base CSS classes', () => {
      const { container } = renderDeckColorCell();
      const cell = container.querySelector('.table__cell');

      expect(cell).toHaveClass('flex');
      expect(cell).toHaveClass('justify-center');
      expect(cell).toHaveClass('table__cell');
    });

    it('applies cursor pointer style', () => {
      const { container } = renderDeckColorCell();
      const cell = container.querySelector('.table__cell');

      expect(cell).toHaveStyle({ cursor: 'pointer' });
    });

    it('applies dark class when dark prop is true', () => {
      const { container } = renderDeckColorCell({ dark: true });
      const cell = container.querySelector('.table__cell');

      expect(cell).toHaveClass('cell__dark');
    });

    it('does not apply dark class when dark prop is false', () => {
      const { container } = renderDeckColorCell({ dark: false });
      const cell = container.querySelector('.table__cell');

      expect(cell).not.toHaveClass('cell__dark');
    });
  });

  describe('cell border styling', () => {
    it('applies multiple borders when multiple cellBorders are true', () => {
      const cellBorders = createMockCellBorders({
        top: true,
        left: true,
        right: true,
        bottom: true,
      });
      const { container } = renderDeckColorCell({ cellBorders });
      const cell = container.querySelector('.table__cell');

      expect(cell).toHaveClass('cell-border-top');
      expect(cell).toHaveClass('cell-border-left');
      expect(cell).toHaveClass('cell-border-right');
      expect(cell).toHaveClass('cell-border-bottom');
    });

    it('does not apply border classes when cellBorders are false', () => {
      const cellBorders = createMockCellBorders({
        top: false,
        left: false,
        right: false,
        bottom: false,
      });
      const { container } = renderDeckColorCell({ cellBorders });
      const cell = container.querySelector('.table__cell');

      expect(cell).not.toHaveClass('cell-border-top');
      expect(cell).not.toHaveClass('cell-border-left');
      expect(cell).not.toHaveClass('cell-border-right');
      expect(cell).not.toHaveClass('cell-border-bottom');
    });
  });

  describe('when deck has no colors', () => {
    it('displays dash when all colors are false', () => {
      const deck = create(FakeDeck, {
        colorWhite: false,
        colorBlue: false,
        colorBlack: false,
        colorRed: false,
        colorGreen: false,
      });

      renderDeckColorCell({ deck });

      expect(screen.getByText('-')).toBeInTheDocument();
    });

    it('does not display color indicators when all colors are false', () => {
      const deck = create(FakeDeck, {
        colorWhite: false,
        colorBlue: false,
        colorBlack: false,
        colorRed: false,
        colorGreen: false,
      });

      const { container } = renderDeckColorCell({ deck });

      expect(container.querySelector('.deck-tile__colors__color')).not.toBeInTheDocument();
    });
  });

  describe('when deck has colors', () => {
    it('displays color indicators when colors are enabled', () => {
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: false,
        colorBlack: true,
        colorRed: false,
        colorGreen: true,
      });

      const { container } = renderDeckColorCell({ deck });

      const colorIndicators = container.querySelectorAll('.deck-tile__colors__color');
      expect(colorIndicators).toHaveLength(5); // All 5 colors should be rendered
    });

    it('does not display dash when colors are enabled', () => {
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: false,
        colorBlack: false,
        colorRed: false,
        colorGreen: false,
      });

      renderDeckColorCell({ deck });

      expect(screen.queryByText('-')).not.toBeInTheDocument();
    });

    it('applies "on" class to enabled colors', () => {
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: false,
        colorBlack: true,
        colorRed: false,
        colorGreen: true,
      });

      const { container } = renderDeckColorCell({ deck });

      const whiteColor = container.querySelector('.is-white');
      const blueColor = container.querySelector('.is-blue');
      const blackColor = container.querySelector('.is-black');
      const redColor = container.querySelector('.is-red');
      const greenColor = container.querySelector('.is-green');

      expect(whiteColor).toHaveClass('on');
      expect(blueColor).not.toHaveClass('on');
      expect(blackColor).toHaveClass('on');
      expect(redColor).not.toHaveClass('on');
      expect(greenColor).toHaveClass('on');
    });

    it('applies correct color classes', () => {
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: true,
        colorBlack: true,
        colorRed: true,
        colorGreen: true,
      });

      const { container } = renderDeckColorCell({ deck });

      expect(container.querySelector('.is-white')).toBeInTheDocument();
      expect(container.querySelector('.is-blue')).toBeInTheDocument();
      expect(container.querySelector('.is-black')).toBeInTheDocument();
      expect(container.querySelector('.is-red')).toBeInTheDocument();
      expect(container.querySelector('.is-green')).toBeInTheDocument();
    });

    it('applies deck-tile__colors__color class to all color indicators', () => {
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: true,
        colorBlack: false,
        colorRed: false,
        colorGreen: false,
      });

      const { container } = renderDeckColorCell({ deck });

      const colorIndicators = container.querySelectorAll('.deck-tile__colors__color');
      colorIndicators.forEach((indicator) => {
        expect(indicator).toHaveClass('deck-tile__colors__color');
      });
    });
  });

  describe('click handling', () => {
    it('calls onClick with deck when cell is clicked', () => {
      const onClick = vi.fn();
      const deck = create(FakeDeck, { name: 'Test Deck' });

      const { container } = renderDeckColorCell({ deck, onClick });
      const cell = container.querySelector('.table__cell');

      if (cell) {
        fireEvent.click(cell);
        expect(onClick).toHaveBeenCalledWith(deck);
      }
    });

    it('calls onClick when dash is displayed', () => {
      const onClick = vi.fn();
      const deck = create(FakeDeck, {
        colorWhite: false,
        colorBlue: false,
        colorBlack: false,
        colorRed: false,
        colorGreen: false,
      });

      const { container } = renderDeckColorCell({ deck, onClick });
      const cell = container.querySelector('.table__cell');

      if (cell) {
        fireEvent.click(cell);
        expect(onClick).toHaveBeenCalledWith(deck);
      }
    });

    it('calls onClick when color indicators are displayed', () => {
      const onClick = vi.fn();
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: false,
        colorBlack: false,
        colorRed: false,
        colorGreen: false,
      });

      const { container } = renderDeckColorCell({ deck, onClick });
      const cell = container.querySelector('.table__cell');

      if (cell) {
        fireEvent.click(cell);
        expect(onClick).toHaveBeenCalledExactlyOnceWith(deck);
      }
    });
  });

  describe('color combinations', () => {
    it('handles single color deck', () => {
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: false,
        colorBlack: false,
        colorRed: false,
        colorGreen: false,
      });

      const { container } = renderDeckColorCell({ deck });

      const onColors = container.querySelectorAll('.deck-tile__colors__color.on');
      expect(onColors).toHaveLength(1);
      expect(container.querySelector('.is-white')).toHaveClass('on');
    });

    it('handles five color deck', () => {
      const deck = create(FakeDeck, {
        colorWhite: true,
        colorBlue: true,
        colorBlack: true,
        colorRed: true,
        colorGreen: true,
      });

      const { container } = renderDeckColorCell({ deck });

      const onColors = container.querySelectorAll('.deck-tile__colors__color.on');
      expect(onColors).toHaveLength(5);
    });
  });
});

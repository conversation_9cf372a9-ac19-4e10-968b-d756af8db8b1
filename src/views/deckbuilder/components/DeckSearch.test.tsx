import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { DeckSearch } from './DeckSearch';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockQuery = vi.mocked(DeckActions.query);

// Mock reduceToFilterState
vi.mock('../../../models/filters/FilterState', async () => {
  const actual = (await vi.importActual('../../../models/filters/FilterState')) as any;
  return {
    ...actual,
    reduceToFilterState: vi.fn(() => actual.FilterState.INACTIVE),
  };
});

type DeckSearchProps = React.ComponentProps<typeof DeckSearch>;

const defaultProps: Omit<DeckSearchProps, 'dispatcher'> = {
  query: '',
};

const renderDeckSearch = (props: Partial<DeckSearchProps> = {}) => {
  return renderWithDispatcher(DeckSearch, { ...defaultProps, ...props });
};

describe('DeckSearch', () => {
  describe('when component renders', () => {
    it('displays the search form', () => {
      const { container } = renderDeckSearch();
      expect(container.querySelector('.deck-search__form')).toBeInTheDocument();
    });

    it('displays placeholder text when query is empty', () => {
      renderDeckSearch({ query: '' });
      expect(screen.getByText('Search your decks')).toBeInTheDocument();
    });

    it('displays the search input', () => {
      renderDeckSearch();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
      expect(screen.getByRole('textbox').classList).toContain('deck-search__form__input');
    });

    it('displays formatted input container', () => {
      const { container } = renderDeckSearch();
      expect(container.querySelector('.deck-search__form__formatted-input')).toBeInTheDocument();
    });

    it('initializes with query from props', () => {
      renderDeckSearch({ query: 'test query' });
      const input = screen.getByRole('textbox') as HTMLInputElement;
      expect(input.value).toBe('test query');
    });
  });

  describe('query input handling', () => {
    it('updates query when input changes', () => {
      renderDeckSearch();
      const input = screen.getByRole('textbox') as HTMLInputElement;

      fireEvent.change(input, { target: { value: 'new query' } });

      expect(input.value).toBe('new query');
    });

    it('prevents default on input change', () => {
      renderDeckSearch();
      const input = screen.getByRole('textbox') as HTMLInputElement;

      // Use fireEvent.change which properly simulates React's SyntheticEvent
      const preventDefaultSpy = vi.spyOn(Event.prototype, 'preventDefault');

      fireEvent.change(input, { target: { value: 'test' } });

      expect(preventDefaultSpy).toHaveBeenCalled();
      preventDefaultSpy.mockRestore();
    });

    it('updates state when props change', () => {
      const { rerenderWithDispatcher } = renderDeckSearch({ query: 'initial' });

      rerenderWithDispatcher({ query: 'updated' });

      const input = screen.getByRole('textbox') as HTMLInputElement;
      expect(input.value).toBe('updated');
    });

    it('provides color filter suggestions', async () => {
      renderDeckSearch({ query: '' });
      screen.getByRole('textbox').focus();
      fireEvent.change(screen.getByRole('textbox'), { target: { value: 'c:' } });
      expect(screen.getByRole('textbox')).toHaveValue('c:');
      await waitFor(() => {
        expect(screen.getByText('white')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('white'));
      expect(screen.getByRole('textbox')).toHaveValue('c:white');
    });
  });

  describe('form submission', () => {
    it('calls DeckActions.query when form is submitted', () => {
      const { dispatcher, container } = renderDeckSearch({ query: 'test query' });
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;

      fireEvent.submit(form);

      expect(mockQuery).toHaveBeenCalledWith(
        'test query',
        expect.any(String),
        expect.any(Object), // ColorFilter
        expect.any(Object), // tags Map
        expect.any(Object), // notTags Map
        dispatcher,
      );
    });

    it('prevents default on form submission', () => {
      const { container } = renderDeckSearch();
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;

      const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(submitEvent, 'preventDefault');
      const stopPropagationSpy = vi.spyOn(submitEvent, 'stopPropagation');

      fireEvent(form, submitEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('extracts color filters from query', () => {
      const { dispatcher, container } = renderDeckSearch({ query: 'c:white c:blue' });
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;

      fireEvent.submit(form);

      expect(mockQuery).toHaveBeenCalledWith(
        'c:white c:blue',
        expect.any(String),
        expect.objectContaining({
          white: expect.any(String),
          blue: expect.any(String),
        }),
        expect.any(Object),
        expect.any(Object),
        dispatcher,
      );
    });
  });

  describe('focus and blur handling', () => {
    it('adds focus class when input is focused', () => {
      const { container } = renderDeckSearch();
      const input = screen.getByRole('textbox') as HTMLInputElement;

      fireEvent.focus(input);

      const form = container.querySelector('.deck-search__form');
      expect(form).toHaveClass('is-focus');
    });

    it('adds document click listener on focus', () => {
      const addEventListenerSpy = vi.spyOn(document, 'addEventListener');
      renderDeckSearch();
      const input = screen.getByRole('textbox') as HTMLInputElement;

      fireEvent.focus(input);

      expect(addEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function));
    });

    it('removes focus class on document click', () => {
      const { container } = renderDeckSearch();
      const input = screen.getByRole('textbox') as HTMLInputElement;

      fireEvent.focus(input);

      // Simulate document click
      fireEvent.click(document);

      const form = container.querySelector('.deck-search__form');
      expect(form).not.toHaveClass('is-focus');
    });
  });

  describe('keyboard handling', () => {
    describe('ENTER key (keyCode 13)', () => {
      it('replaces query with selected suggestion when suggestions exist', async () => {
        renderDeckSearch({ query: '' });
        const input = screen.getByRole('textbox') as HTMLInputElement;

        // Focus input and trigger color suggestions
        fireEvent.focus(input);
        fireEvent.change(input, { target: { value: 'c:bl' } });

        // Wait for suggestions to appear
        await waitFor(() => {
          expect(screen.getByText('blue')).toBeInTheDocument();
        });

        // Navigate to first suggestion (blue)
        fireEvent.keyDown(input, { keyCode: 40 }); // DOWN to select first suggestion

        // Press ENTER to apply suggestion
        fireEvent.keyDown(input, { keyCode: 13 });

        // Verify the query was updated with the suggestion
        expect(input.value).toBe('c:blue ');
      });
    });

    describe('UP key (keyCode 38)', () => {
      it('decreases suggestionsIndex when suggestions exist', async () => {
        const { container } = renderDeckSearch({ query: '' });
        const input = screen.getByRole('textbox') as HTMLInputElement;

        // Focus input and trigger color suggestions
        fireEvent.focus(input);
        fireEvent.change(input, { target: { value: 'c:' } });

        // Wait for suggestions to appear
        await waitFor(() => {
          expect(screen.getByText('white')).toBeInTheDocument();
        });

        // Navigate down to select a suggestion first
        fireEvent.keyDown(input, { keyCode: 40 }); // DOWN
        fireEvent.keyDown(input, { keyCode: 40 }); // DOWN again

        // Now navigate up
        fireEvent.keyDown(input, { keyCode: 38 }); // UP

        // Should handle the navigation without errors
        expect(container.querySelectorAll('.is-selected')).toHaveLength(1);
        expect(container.querySelectorAll('.is-selected')[0]).toBe(screen.getByText('white'));
      });
    });

    describe('DOWN key (keyCode 40)', () => {
      it('increases suggestionsIndex when suggestions exist', async () => {
        const { container } = renderDeckSearch({ query: '' });
        const input = screen.getByRole('textbox') as HTMLInputElement;

        // Focus input and trigger color suggestions
        fireEvent.focus(input);
        fireEvent.change(input, { target: { value: 'c:' } });

        // Wait for suggestions to appear
        await waitFor(() => {
          expect(screen.getByText('white')).toBeInTheDocument();
        });

        // Navigate down
        fireEvent.keyDown(input, { keyCode: 40 }); // DOWN

        // Should handle the navigation without errors
        expect(container.querySelectorAll('.is-selected')).toHaveLength(1);
        expect(container.querySelectorAll('.is-selected')[0]).toBe(screen.getByText('white'));
      });
    });
  });

  describe('form click handling', () => {
    it('prevents default when form is clicked', () => {
      const { container } = renderDeckSearch();
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;

      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');

      fireEvent(form, clickEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });
});

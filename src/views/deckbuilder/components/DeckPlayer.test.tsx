import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard, FakeDeckCard, createFakeSuggestedCard } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { DeckPlayer } from './DeckPlayer';

// Mock track helper
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

// Mock DeckBuilderBoardItem component
// vi.mock('./DeckBuilderBoardItem', () => ({
//   DeckBuilderBoardItem: ({ card, onClick, onGotoTop, onGotoBottom, onGotoGraveyard }: any) => (
//     <div data-testid="deck-builder-board-item" data-card-name={card?.get?.('name') || 'Unknown'}>
//       <button onClick={onClick}>Card: {card?.get?.('name') || 'Unknown'}</button>
//       <button onClick={onGotoTop}>To Top</button>
//       <button onClick={onGotoBottom}>To Bottom</button>
//       <button onClick={onGotoGraveyard}>Graveyard</button>
//     </div>
//   ),
// }));

// Mock deck calculation functions
vi.mock('../../../models/Decks', async () => {
  const actual = await vi.importActual('../../../models/Decks');
  return {
    ...actual,
    calculateTurnProbabilities: vi.fn().mockReturnValue(
      Immutable.Map({
        'Lightning Bolt': 0.4,
        Mountain: 0.3,
        Shock: 0.3,
      }),
    ),
    findDeckBoardByName: vi.fn().mockReturnValue({
      get: vi.fn((key: string) => {
        if (key === 'id') return 1;
        if (key === 'name') return 'Main';
        return undefined;
      }),
    }),
  };
});

type DeckPlayerProps = React.ComponentProps<typeof DeckPlayer>;

// Mock cards data using existing helper
const createMockCards = () => {
  const fakeCards = createFakeSuggestedCard(8);
  return Immutable.Map([
    [101, fakeCards[0].set('id', 101)],
    [102, fakeCards[1].set('id', 102)],
    [103, fakeCards[2].set('id', 103)],
    [104, fakeCards[3].set('id', 104)],
    [105, fakeCards[4].set('id', 105)],
    [106, fakeCards[5].set('id', 106)],
    [107, fakeCards[6].set('id', 107).set('types', Immutable.List(['Land']))], // Make this card a land
    [108, fakeCards[7].set('id', 108)],
  ]);
};

// Mock deck cards data
const createMockDeckCards = () => {
  return Immutable.Map([
    [1, create(FakeDeckCard, { id: 1, boardId: 1, cardId: 101, cardInstanceId: 201 })],
    [2, create(FakeDeckCard, { id: 2, boardId: 1, cardId: 102, cardInstanceId: 202 })],
    [3, create(FakeDeckCard, { id: 3, boardId: 1, cardId: 103, cardInstanceId: 203 })],
    [4, create(FakeDeckCard, { id: 4, boardId: 1, cardId: 104, cardInstanceId: 204 })],
    [5, create(FakeDeckCard, { id: 5, boardId: 1, cardId: 105, cardInstanceId: 205 })],
    [6, create(FakeDeckCard, { id: 6, boardId: 1, cardId: 106, cardInstanceId: 206 })],
    [7, create(FakeDeckCard, { id: 7, boardId: 1, cardId: 107, cardInstanceId: 207 })],
    [8, create(FakeDeckCard, { id: 8, boardId: 2, cardId: 108, cardInstanceId: 208 })], // Sideboard card
  ]);
};

const createMockDeckWithCards = () => {
  const mainBoard = create(FakeDeckBoard, { id: 1, name: 'Main' });
  // Create a deck with both cards and deck cards so the draw pile can show card names
  return create(FakeDeck, {
    boards: Immutable.List([mainBoard]),
    deckCards: createMockDeckCards(),
    cards: createMockCards(),
  });
};

const defaultProps: Omit<DeckPlayerProps, 'dispatcher'> = {
  deck: createMockDeckWithCards(),
};

const renderDeckPlayer = (props: Partial<Omit<DeckPlayerProps, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(DeckPlayer, {
    ...defaultProps,
    ...props,
  });
};

describe('DeckPlayer', () => {
  describe('game sections and controls', () => {
    beforeEach(() => {
      renderDeckPlayer();
    });

    it('renders all sections with no cards', () => {
      expect(screen.getByText('No cards on Battlefield')).toBeInTheDocument();
      expect(screen.getByText('No cards in Graveyard')).toBeInTheDocument();
      expect(screen.getByText('No cards in Hand')).toBeInTheDocument();
    });

    it('displays New Game button', () => {
      expect(screen.getByRole('button', { name: 'New Game' })).toBeInTheDocument();
    });

    it('displays Next Turn button', () => {
      expect(screen.getByRole('button', { name: 'Next Turn' })).toBeInTheDocument();
    });

    it('displays Draw Card button', () => {
      expect(screen.getByRole('button', { name: 'Draw Card' })).toBeInTheDocument();
    });

    it('displays Shuffle button', () => {
      expect(screen.getByRole('button', { name: 'Shuffle' })).toBeInTheDocument();
    });

    it('displays Mulligan button on turn 1', () => {
      expect(screen.getByRole('button', { name: 'Mulligan' })).toBeInTheDocument();
    });

    it('displays turn number', () => {
      expect(screen.getByText('Turn number: 1')).toBeInTheDocument();
    });

    it('displays cards in hand count', () => {
      expect(screen.getByText(/Cards in hand: \d+/)).toBeInTheDocument();
    });

    it('displays a table with 7 cards', () => {
      expect(document.querySelector('.deck-table')).toBeInTheDocument();

      // Should have a row for each card in the draw pile (7 main board cards)
      const cardRows = document.querySelectorAll('.deck-table__row');
      // 7 rows, 3 columns. All cell have the above class name unfortunately
      expect(cardRows).toHaveLength(7 * 3);
    });
  });

  describe('tutor functionality', () => {
    it('displays tutor input field', () => {
      renderDeckPlayer();
      expect(screen.getByPlaceholderText('Tutor a card from your deck')).toBeInTheDocument();
    });

    it('displays Tutor button', () => {
      renderDeckPlayer();
      expect(screen.getByRole('button', { name: 'Tutor' })).toBeInTheDocument();
    });

    it('handles tutor input changes', () => {
      renderDeckPlayer();
      const tutorInput = screen.getByPlaceholderText('Tutor a card from your deck');

      fireEvent.change(tutorInput, { target: { value: 'Lightning' } });

      expect(tutorInput).toHaveValue('Lightning');
    });

    it('submits tutor form when button is clicked', () => {
      renderDeckPlayer();
      const tutorButton = screen.getByRole('button', { name: 'Tutor' });

      fireEvent.click(tutorButton);

      // Should not throw an error
      expect(tutorButton).toBeInTheDocument();
    });
  });

  describe('as a user', () => {
    test('I can draw cards', () => {
      renderDeckPlayer();

      // Initially, hand should be empty
      expect(screen.getByText('No cards in Hand')).toBeInTheDocument();

      const drawCardButton = screen.getByRole('button', { name: 'Draw Card' });
      fireEvent.click(drawCardButton);

      // After drawing, a card should be added to the hand
      const cardInHandSection = document.querySelector('.playtest__hand');
      expect(cardInHandSection).toBeInTheDocument();

      const cardInHand = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      expect(cardInHandSection).toContainElement(cardInHand);
    });

    test('I can send a card to the graveyard', () => {
      renderDeckPlayer();

      fireEvent.click(screen.getByText('Draw Card'));

      const cardInHand = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      fireEvent.mouseOver(cardInHand);
      fireEvent.click(screen.getByText('Graveyard'));

      expect(screen.getByText('No cards in Hand')).toBeInTheDocument();

      const cardInGraveyard = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      expect(document.querySelector('.playtest__graveyard')).toContainElement(cardInGraveyard);
    });

    test('I can revive a card from the graveyard', () => {
      renderDeckPlayer();

      fireEvent.click(screen.getByText('Draw Card'));

      const cardInHand = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      const cardInHandSection = document.querySelector('.playtest__hand');
      expect(cardInHandSection).toContainElement(cardInHand);

      fireEvent.mouseOver(cardInHand);
      fireEvent.click(screen.getByText('Graveyard'));

      expect(screen.getByText('No cards in Hand')).toBeInTheDocument();

      const cardInGraveyard = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      expect(document.querySelector('.playtest__graveyard')).toContainElement(cardInGraveyard);
      fireEvent.click(cardInGraveyard);

      // After clicking the card in graveyard, it should return to hand
      expect(cardInHandSection).toBeInTheDocument();

      // The hand should now contain a card
      const revivedCard = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      expect(cardInHandSection).toContainElement(revivedCard);
    });

    test('creating a new game adds 7 cards to my hand', () => {
      renderDeckPlayer();
      const newGameButton = screen.getByRole('button', { name: 'New Game' });

      fireEvent.click(newGameButton);

      // Should reset the game state
      expect(screen.getByText('Turn number: 1')).toBeInTheDocument();

      // Picks 7 cards from the draw pile
      const cardRows = document.querySelectorAll('.deck-table__row');
      expect(cardRows).toHaveLength(0);

      // Should have 7 cards in hand
      const cardsInHand = document.querySelectorAll('.deckbuilder-board-item__image-wrapper__hover');
      expect(cardsInHand).toHaveLength(7);
    });

    test('taking a mulligan penalises me 1 card', () => {
      renderDeckPlayer();

      // Start a new game to get initial 7 cards
      const newGameButton = screen.getByRole('button', { name: 'New Game' });
      fireEvent.click(newGameButton);

      const mulliganButton = screen.getByRole('button', { name: 'Mulligan' });
      fireEvent.click(mulliganButton);

      // Should have 6 cards in hand
      const cardsInHand = document.querySelectorAll('.deckbuilder-board-item__image-wrapper__hover');
      expect(cardsInHand).toHaveLength(6);
    });

    test('I can move to the Next Turn', () => {
      renderDeckPlayer();
      const nextTurnButton = screen.getByRole('button', { name: 'Next Turn' });

      fireEvent.click(nextTurnButton);

      // Turn number should increment
      expect(screen.getByText('Turn number: 2')).toBeInTheDocument();
    });

    test('I can shuffle my draw pile', () => {
      renderDeckPlayer();

      // Get the initial order of cards in the statistics table
      const initialCardRows = Array.from(document.querySelectorAll('.deck-table__row')).slice(0, 7);
      const initialCardNames = initialCardRows.map((row) => row.textContent || '');

      const shuffleButton = screen.getByRole('button', { name: 'Shuffle' });
      fireEvent.click(shuffleButton);

      // Get the shuffled order of cards
      const shuffledCardRows = Array.from(document.querySelectorAll('.deck-table__row')).slice(0, 7);
      const shuffledCardNames = shuffledCardRows.map((row) => row.textContent || '');

      // Expect that the order has changed
      expect(
        initialCardNames.some((value, index) => {
          return value !== shuffledCardNames[index];
        }),
      ).toBe(true);
    });

    test('I can play a card from hand to battlefield', () => {
      renderDeckPlayer();

      // Draw a card first
      fireEvent.click(screen.getByText('Draw Card'));

      // Click on the card in hand to play it
      const cardInHand = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      fireEvent.click(cardInHand);

      // The card should now be on the battlefield
      const battlefieldElement = document.querySelector('.playtest__battlefield');
      expect(battlefieldElement).toBeInTheDocument();

      const cardOnBattlefield = battlefieldElement!.querySelector('.deckbuilder-board-item') as HTMLElement;
      expect(cardOnBattlefield).toBeInTheDocument();
      expect(battlefieldElement).toContainElement(cardOnBattlefield);
    });

    test('I can return a card from battlefield to hand', () => {
      renderDeckPlayer();

      // Draw a card first
      fireEvent.click(screen.getByText('Draw Card'));

      // Click on the card in hand to play it to battlefield
      const cardInHand = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      fireEvent.click(cardInHand);

      // Verify card is on battlefield
      const battlefieldElement = document.querySelector('.playtest__battlefield');
      const cardOnBattlefield = battlefieldElement!.querySelector('.deckbuilder-board-item') as HTMLElement;
      expect(battlefieldElement).toContainElement(cardOnBattlefield);

      // Click on the card on battlefield to return it to hand
      fireEvent.click(cardOnBattlefield);

      // The card should now be back in hand
      const handElement = document.querySelector('.playtest__hand');
      expect(handElement).toBeInTheDocument();

      const cardBackInHand = handElement!.querySelector('.deckbuilder-board-item') as HTMLElement;
      expect(cardBackInHand).toBeInTheDocument();
      expect(handElement).toContainElement(cardBackInHand);
    });

    test('I can return a card from hand to top of draw pile', () => {
      renderDeckPlayer();

      const firstCardInDrawPileName = document.querySelector('.deck-table__row')?.textContent;

      // Draw a card first
      fireEvent.click(screen.getByText('Draw Card'));

      expect(document.querySelector('.deck-table__row')?.textContent).not.toBe(firstCardInDrawPileName);

      // Get the card in hand and hover over it to show options
      const cardInHand = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      fireEvent.mouseOver(cardInHand);

      // Click "Return to Top" option
      fireEvent.click(screen.getByText('Return to Top'));

      // The card should be removed from hand
      expect(screen.getByText('No cards in Hand')).toBeInTheDocument();

      // The card should be back on top of the draw pile
      expect(document.querySelector('.deck-table__row')?.textContent).toBe(firstCardInDrawPileName);
    });

    test('I can return a card from hand to bottom of draw pile', () => {
      renderDeckPlayer();

      const firstCardInDrawPileName = document.querySelector('.deck-table__row')?.textContent;

      // Draw a card first
      fireEvent.click(screen.getByText('Draw Card'));

      // Get the card in hand and hover over it to show options
      const cardInHand = document.querySelector('.deckbuilder-board-item__image-wrapper__hover')! as HTMLElement;
      fireEvent.mouseOver(cardInHand);

      // Click "Return to Bottom" option
      fireEvent.click(screen.getByText('Return to Bottom'));

      // The card should be removed from hand
      expect(screen.getByText('No cards in Hand')).toBeInTheDocument();

      // The card should be back on bottom of the draw pile
      const cardRows = document.querySelectorAll('.deck-table__row');
      expect(cardRows[6].textContent).toBe(firstCardInDrawPileName);
    });

    test('I can search for cards in the draw pile', () => {
      renderDeckPlayer();

      // Get the tutor input field
      const tutorInput = screen.getByPlaceholderText('Tutor a card from your deck');
      expect(tutorInput).toBeInTheDocument();

      const searchName = defaultProps.deck.get('cards').get(101).get('name');

      // Type a search term
      fireEvent.change(tutorInput, { target: { value: searchName } });
      fireEvent.focus(tutorInput);

      expect(document.querySelector('.deck-player__suggestions')).toHaveTextContent(searchName);
      const suggestion = screen.getByText(searchName, {
        selector: '.react-select__suggestion__label__name',
      });

      fireEvent.click(suggestion);

      // The search input should have the suggestion text
      expect(tutorInput).toHaveValue(searchName);
    });

    test('I can select the first suggestions with enter', () => {
      renderDeckPlayer();

      // Get the tutor input field
      const tutorInput = screen.getByPlaceholderText('Tutor a card from your deck');

      const searchName = document.querySelector('.deck-table__row')?.textContent;

      if (!searchName) {
        throw new Error('Failed to get first card name');
      }

      // Type a search term
      fireEvent.change(tutorInput, { target: { value: searchName } });
      fireEvent.focus(tutorInput);

      expect(document.querySelector('.deck-player__suggestions')).toHaveTextContent(searchName);

      fireEvent.keyDown(tutorInput, { keyCode: 13 });

      // The search input should have the suggestion text
      expect(tutorInput).toHaveValue(searchName);
    });

    test('I can use keyboard to select a card from the draw pile', () => {
      renderDeckPlayer();

      // Get the tutor input field
      const tutorInput = screen.getByPlaceholderText('Tutor a card from your deck');

      fireEvent.change(tutorInput, { target: { value: 'a' } });
      fireEvent.change(tutorInput, { target: { value: '' } });
      fireEvent.focus(tutorInput);

      expect(document.querySelectorAll('.deck-player__suggestion')).toHaveLength(7);

      const firstSuggestionName = document.querySelector('.deck-table__row')?.textContent;

      if (!firstSuggestionName) {
        throw new Error('Failed to get first suggestion name');
      }

      // The first suggestion should be equal to the first card in the draw pile
      expect(document.querySelector('.deck-player__suggestion')).toHaveTextContent(firstSuggestionName);

      fireEvent.keyDown(tutorInput, { keyCode: 40 });
      expect(document.querySelector('.deck-player__suggestion')).toHaveClass('is-selected');

      fireEvent.keyDown(tutorInput, { keyCode: 40 });
      expect(document.querySelectorAll('.deck-player__suggestion')[1]).toHaveClass('is-selected');

      fireEvent.keyDown(tutorInput, { keyCode: 38 });
      expect(document.querySelector('.deck-player__suggestion')).toHaveClass('is-selected');

      fireEvent.keyDown(tutorInput, { keyCode: 13 });
      expect(tutorInput).toHaveValue(firstSuggestionName);
    });

    test('I can play a land card to the lands section of battlefield', () => {
      renderDeckPlayer();

      // Get the tutor input field to search for the land card
      const tutorInput = screen.getByPlaceholderText('Tutor a card from your deck');

      const landCardName = defaultProps.deck.get('cards').get(107).get('name');

      // Search for the land card (card ID 107 which we made a land)
      fireEvent.change(tutorInput, { target: { value: landCardName } });
      fireEvent.focus(tutorInput);

      // Click on the land card suggestion to add it to hand
      const landSuggestion = screen.getByText(landCardName, {
        selector: '.react-select__suggestion__label__name',
      });
      fireEvent.click(landSuggestion);

      fireEvent.click(screen.getByText('Tutor'));

      // Click on the land card in hand to play it
      const cardInHand = document.querySelector('.deckbuilder-board-item')! as HTMLElement;
      fireEvent.click(cardInHand);

      // The land card should be added to the second occurrence of .playtest__battlefield__items
      const battlefieldItemsSections = document.querySelectorAll('.playtest__battlefield__items');
      expect(battlefieldItemsSections).toHaveLength(2);

      const landsSection = battlefieldItemsSections[1]; // Second occurrence for lands
      const landCard = landsSection.querySelector('.deckbuilder-board-item') as HTMLElement;
      expect(landCard).toBeInTheDocument();
      expect(landsSection).toContainElement(landCard);
    });
  });
});

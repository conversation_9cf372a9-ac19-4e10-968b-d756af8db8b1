import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DecksModule from '../../../models/Decks';
import { DeckCardGrouping } from '../../../models/Decks';
import { DeckStatistics } from './DeckStatistics';

// Mock Plotly for chart components
const mockNewPlot = vi.hoisted(() => vi.fn());
const mockPurge = vi.hoisted(() => vi.fn());

vi.mock('../../../helpers/plotly', () => ({
  default: () => ({
    newPlot: mockNewPlot,
    purge: mockPurge,
  }),
}));

// Mock serverside helper
vi.mock('../../../helpers/serverside', () => ({
  default: vi.fn(() => false),
}));

// Mock the deck calculation functions
vi.mock('../../../models/Decks', async () => {
  const actual = await vi.importActual('../../../models/Decks');
  return {
    ...actual,
    calculateCardTypeRatios: vi.fn(),
    calculateManaSymbolRatios: vi.fn(),
    calculateManaSymbolStack: vi.fn(),
    calculateTurnProbabilities: vi.fn(),
  };
});

const mockCalculateCardTypeRatios = vi.mocked(DecksModule.calculateCardTypeRatios);
const mockCalculateManaSymbolRatios = vi.mocked(DecksModule.calculateManaSymbolRatios);
const mockCalculateManaSymbolStack = vi.mocked(DecksModule.calculateManaSymbolStack);
const mockCalculateTurnProbabilities = vi.mocked(DecksModule.calculateTurnProbabilities);

type DeckStatisticsProps = React.ComponentProps<typeof DeckStatistics>;

const defaultProps: Omit<DeckStatisticsProps, 'dispatcher'> = {
  deck: create(FakeDeck),
};

const renderDeckStatistics = (props: Partial<DeckStatisticsProps> = {}) => {
  return renderWithDispatcher(DeckStatistics, {
    ...defaultProps,
    ...props,
  });
};

describe('DeckStatistics', () => {
  beforeEach(() => {
    // Setup default mock return values
    mockCalculateCardTypeRatios.mockReturnValue(
      Immutable.Map({
        Creature: 0.4,
        Instant: 0.3,
        Sorcery: 0.2,
        Land: 0.1,
      }),
    );

    mockCalculateManaSymbolRatios.mockReturnValue(
      Immutable.Map({
        totalCount: 60,
        red: 0.25,
        blue: 0.15,
        white: 0.35,
        black: 0.15,
        green: 0.1,
        disabled: 0,
      }),
    );

    mockCalculateManaSymbolStack.mockReturnValue(
      Immutable.Map({
        white: Immutable.Map<number, number>([
          [1, 5],
          [2, 3],
          [3, 2],
        ]),
        blue: Immutable.Map<number, number>([
          [1, 4],
          [2, 6],
          [3, 1],
        ]),
        red: Immutable.Map<number, number>([
          [1, 8],
          [2, 2],
          [3, 1],
        ]),
        green: Immutable.Map<number, number>([
          [1, 2],
          [2, 5],
          [3, 4],
        ]),
        black: Immutable.Map<number, number>([
          [1, 1],
          [2, 3],
          [3, 6],
        ]),
        multi: Immutable.Map<number, number>([
          [1, 1],
          [2, 2],
          [3, 1],
        ]),
        colorless: Immutable.Map<number, number>([
          [1, 2],
          [2, 1],
          [3, 0],
        ]),
      }),
    );

    mockCalculateTurnProbabilities.mockReturnValue(
      Immutable.Map({
        'Lightning Bolt': 0.4,
        Mountain: 0.3,
        Shock: 0.3,
      }),
    );
  });

  describe('when component renders', () => {
    it('displays the deck statistics container', () => {
      const { container } = renderDeckStatistics();
      expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
    });

    it('calls calculation functions with the provided deck', () => {
      const deck = create(FakeDeck);
      renderDeckStatistics({ deck });

      expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(deck);
      expect(mockCalculateManaSymbolRatios).toHaveBeenCalledWith(deck);
      expect(mockCalculateManaSymbolStack).toHaveBeenCalledWith(deck);
      expect(mockCalculateTurnProbabilities).toHaveBeenCalledWith(deck, DeckCardGrouping.NAME, true);
    });

    it('renders card types statistics chart container', () => {
      renderDeckStatistics();
      expect(document.querySelector('#Card\\ Types')).toBeInTheDocument();
    });

    it('renders mana symbols statistics chart container', () => {
      renderDeckStatistics();
      expect(document.querySelector('#Mana\\ Symbols')).toBeInTheDocument();
    });

    it('renders mana curve statistics chart container', () => {
      renderDeckStatistics();
      expect(document.querySelector('#Mana\\ Curve')).toBeInTheDocument();
    });

    it('renders turn probabilities section', () => {
      renderDeckStatistics();
      expect(screen.getByText('Turn Probabilities')).toBeInTheDocument();
    });
  });

  describe('turn probabilities section', () => {
    it('displays grouping selector', () => {
      renderDeckStatistics();
      expect(screen.getByText('Group by')).toBeInTheDocument();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('displays grouping options excluding NONE', () => {
      renderDeckStatistics();

      // Check that grouping options are present (excluding NONE)
      expect(screen.getByRole('option', { name: DeckCardGrouping.NAME })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: DeckCardGrouping.CARD_TYPE })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: DeckCardGrouping.MANA_COST })).toBeInTheDocument();
    });

    it('displays turn columns for turns 1, 2, and 3', () => {
      renderDeckStatistics();
      expect(screen.getByText('Turn 1')).toBeInTheDocument();
      expect(screen.getByText('Turn 2')).toBeInTheDocument();
      expect(screen.getByText('Turn 3')).toBeInTheDocument();
    });

    it('changes grouping when selection changes', () => {
      renderDeckStatistics();
      const select = screen.getByRole('combobox');

      fireEvent.change(select, { target: { value: DeckCardGrouping.CARD_TYPE } });

      expect(select).toHaveValue(DeckCardGrouping.CARD_TYPE);
      expect(mockCalculateTurnProbabilities).toHaveBeenCalledWith(expect.any(Object), DeckCardGrouping.CARD_TYPE, true);
    });
  });

  describe('chart pills rendering', () => {
    it('renders card type chart pills with percentages', () => {
      renderDeckStatistics();

      // Should display percentages for card types
      expect(screen.getByText('40%')).toBeInTheDocument(); // Creature
      expect(screen.getByText('30%')).toBeInTheDocument(); // Instant
      expect(screen.getByText('20%')).toBeInTheDocument(); // Sorcery
      expect(screen.getAllByText('10%').length).toBeGreaterThan(0); // Land (may appear multiple times)
    });

    it('renders mana symbol chart pills with percentages', () => {
      renderDeckStatistics();

      // Should display percentages for mana symbols based on mock data
      expect(screen.getByText('35%')).toBeInTheDocument(); // white
      expect(screen.getByText('25%')).toBeInTheDocument(); // red
      expect(screen.getAllByText('15%').length).toBeGreaterThanOrEqual(2); // blue and black
      expect(screen.getAllByText('10%').length).toBeGreaterThan(0); // green (may appear multiple times)
    });
  });

  describe('color mapping', () => {
    it('applies correct background colors to card type chart pills', () => {
      const { container } = renderDeckStatistics();

      // Find chart pills and verify they have the expected background colors
      const chartPills = container.querySelectorAll('.chart-pill');

      // Based on mock data: Creature (40%), Instant (30%), Sorcery (20%), Land (10%)
      // Expected colors from keyToColor method:
      // Creature: #1AC655, Instant: #D61616, Sorcery: #0A73EE, Land: #614A3A

      const pillsArray = Array.from(chartPills);
      const creaturePill = pillsArray.find((pill) => pill.textContent?.includes('Creature'));
      const instantPill = pillsArray.find((pill) => pill.textContent?.includes('Instant'));
      const sorceryPill = pillsArray.find((pill) => pill.textContent?.includes('Sorcery'));
      const landPill = pillsArray.find((pill) => pill.textContent?.includes('Land'));

      expect(creaturePill).toHaveStyle('background-color: rgb(26, 198, 85)'); // #1AC655
      expect(instantPill).toHaveStyle('background-color: rgb(214, 22, 22)'); // #D61616
      expect(sorceryPill).toHaveStyle('background-color: rgb(10, 115, 238)'); // #0A73EE
      expect(landPill).toHaveStyle('background-color: rgb(97, 74, 58)'); // #614A3A
    });

    it('applies correct background colors to mana symbol chart pills', () => {
      const { container } = renderDeckStatistics();

      // Find mana symbol chart pills
      const chartPills = container.querySelectorAll('.chart-pill');

      // Based on mock data: white (35%), red (25%), blue (15%), black (15%), green (10%)
      // Expected colors from keyToColor method:
      // white: #FFE48B, red: #FF5C35, blue: #45C3FF, black: #676767, green: #1AC655

      const pillsArray = Array.from(chartPills);
      const whitePill = pillsArray.find((pill) => pill.textContent?.includes('White'));
      const redPill = pillsArray.find((pill) => pill.textContent?.includes('Red'));
      const bluePill = pillsArray.find((pill) => pill.textContent?.includes('Blue'));
      const blackPill = pillsArray.find((pill) => pill.textContent?.includes('Black'));
      const greenPill = pillsArray.find((pill) => pill.textContent?.includes('Green'));

      expect(whitePill).toHaveStyle('background-color: rgb(255, 228, 139)'); // #FFE48B
      expect(redPill).toHaveStyle('background-color: rgb(255, 92, 53)'); // #FF5C35
      expect(bluePill).toHaveStyle('background-color: rgb(69, 195, 255)'); // #45C3FF
      expect(blackPill).toHaveStyle('background-color: rgb(103, 103, 103)'); // #676767
      expect(greenPill).toHaveStyle('background-color: rgb(26, 198, 85)'); // #1AC655
    });
  });
});

import { faker } from '@faker-js/faker';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { create, FakeCardSet } from '../../../tests/fake/Fake';
import { renderWithDispatcher } from '../../../tests/test-utils';
import * as FilterActions from '../../actions/FilterActions';
import history from '../../helpers/history';
import { SetCompletion } from '../../models/SetCompletion';
import { SetCompletionCell } from './SetCompletionCell';

vi.mock('../../actions/FilterActions', () => ({
  overrideWithCardSet: vi.fn(),
}));

vi.mock('../../helpers/history', () => ({
  default: {
    push: vi.fn(),
  },
}));

const mockOverrideWithCardSet = vi.mocked(FilterActions.overrideWithCardSet);
const mockHistoryPush = vi.mocked(history.push);

type SetCompletionCellProps = ComponentProps<typeof SetCompletionCell>;

const mockUsername = faker.internet.userName();

// Helper to create mock SetCompletion
const createMockSetCompletion = (overrides: Record<string, any> = {}) => {
  const cardSet = create(FakeCardSet, overrides.cardSet || {});
  return new SetCompletion({
    cardSet,
    totalCards: faker.number.int({ min: 50, max: 300 }),
    totalValue: faker.number.float({ min: 100, max: 5000 }),
    totalOwned: faker.number.int({ min: 0, max: 100 }),
    uniqueOwned: faker.number.int({ min: 0, max: 50 }),
    completion: faker.number.float({ min: 0, max: 1 }),
    ...overrides,
  });
};

const mockSetCompletion = createMockSetCompletion();

const DEFAULT_PROPS: Omit<SetCompletionCellProps, 'dispatcher'> = {
  setCompletion: mockSetCompletion,
  username: mockUsername,
  disabled: false,
};

const renderSetCompletionCell = (props: Partial<Omit<SetCompletionCellProps, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(SetCompletionCell, { ...DEFAULT_PROPS, ...props });
};

describe('SetCompletionCell', () => {
  it('displays set information', () => {
    renderSetCompletionCell();

    expect(screen.getByText(mockSetCompletion.get('cardSet').get('name'))).toBeInTheDocument();
    expect(
      screen.getByText(`${mockSetCompletion.get('uniqueOwned')} / ${mockSetCompletion.get('totalCards')}`),
    ).toBeInTheDocument();
    expect(screen.getByText(mockSetCompletion.get('cardSet').setTypeToString())).toBeInTheDocument();
  });
  test('triggers filterBySet function and navigates to collection', async () => {
    mockOverrideWithCardSet.mockResolvedValue(undefined);
    const { dispatcher } = renderSetCompletionCell();

    const setName = mockSetCompletion.get('cardSet').get('name');
    const clickableContainer = screen.getByText(setName).closest('.flex');
    fireEvent.click(clickableContainer!);

    await waitFor(() => {
      expect(mockOverrideWithCardSet).toHaveBeenCalledWith(
        mockSetCompletion.get('cardSet'),
        expect.anything(),
        dispatcher,
      );
      expect(mockHistoryPush).toHaveBeenCalledWith('/collection');
    });
  });
  describe('when disabled prop is true', () => {
    it('hides set type', () => {
      renderSetCompletionCell({ disabled: true });
      expect(screen.queryByText(mockSetCompletion.get('cardSet').setTypeToString())).not.toBeInTheDocument();
    });
    test('does not call filterBySet function', () => {
      renderSetCompletionCell({ disabled: true });

      const setName = mockSetCompletion.get('cardSet').get('name');
      const clickableContainer = screen.getByText(setName).closest('.flex');
      fireEvent.click(clickableContainer!);

      expect(mockOverrideWithCardSet).not.toHaveBeenCalled();
      expect(mockHistoryPush).not.toHaveBeenCalled();
    });
  });

  describe('percentage display', () => {
    it('shows correct percentages', () => {
      const completionValues = [
        { completion: 1.0, expected: '100%' },
        { completion: 0.999, expected: '99.9%' },
        { completion: 0.75, expected: '75%' },
        { completion: 0, expected: '0%' },
      ];

      completionValues.forEach(({ completion, expected }) => {
        const mockSetCompletion = createMockSetCompletion({ completion });
        renderSetCompletionCell({ setCompletion: mockSetCompletion });
        expect(screen.getByText(expected)).toBeInTheDocument();
      });
    });

    it('handles invalid completion values greater than 1', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const mockSetCompletion = createMockSetCompletion({ completion: 1.5 });

      renderSetCompletionCell({ setCompletion: mockSetCompletion });

      expect(screen.getByText('100%')).toBeInTheDocument();
      expect(consoleSpy).toHaveBeenCalledWith('toPercentage completion === 1.5, which is greater than 1');

      consoleSpy.mockRestore();
    });
  });
});

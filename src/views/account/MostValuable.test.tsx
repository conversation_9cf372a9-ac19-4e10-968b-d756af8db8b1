import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { create, FakeMTGCardGroup } from '../../../tests/fake/Fake';
import { renderWithDispatcher } from '../../../tests/test-utils';
import * as StatisticsActions from '../../actions/StatisticsActions';
import displayCurrency from '../../helpers/currency_helper';
import { CardInstance } from '../../models/CardInstances';
import { Foil } from '../../models/Foil';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
import { MostValuable } from './MostValuable';

vi.mock('../../actions/StatisticsActions', () => ({
  mostValuableCard: vi.fn(),
  changeStatus: vi.fn(),
}));

vi.mock('../../helpers/core_assets', () => ({
  CoreAssets: {
    cardBack: vi.fn(() => 'mock-card-back-url'),
    imageHost: vi.fn(() => 'mock-image-host'),
  },
  ImageQuality: {
    HQ: 'HQ',
  },
}));

const mockMostValuableCard = vi.mocked(StatisticsActions.mostValuableCard);
const mockChangeStatus = vi.mocked(StatisticsActions.changeStatus);

type MostValuableProps = ComponentProps<typeof MostValuable>;

const mockCardGroup = create(FakeMTGCardGroup);

const DEFAULT_PROPS: Omit<MostValuableProps, 'dispatcher'> = {
  currencySymbol: '$',
  cardGroup: create(FakeMTGCardGroup),
  status: StatComponentStatus.LOADED,
};

const renderMostValuable = (props: Partial<Omit<MostValuableProps, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(MostValuable, { ...DEFAULT_PROPS, ...props });
};

describe('MostValuable', () => {
  it('displays most valuable card information when loaded', () => {
    const cardGroup = create(FakeMTGCardGroup);
    const cardInstance = cardGroup.get('cardInstances').first()!;
    renderMostValuable({ cardGroup });

    expect(screen.getByText(cardInstance.get('cardName'))).toBeInTheDocument();

    const expectedPrice = displayCurrency(cardInstance.get('cardPrice'), true, '$');
    expect(screen.getByText(expectedPrice)).toBeInTheDocument();
  });

  describe('when loading', () => {
    it('shows loading state for INACTIVE status', () => {
      renderMostValuable({ status: StatComponentStatus.INACTIVE });
      expect(screen.getAllByText('-')).toHaveLength(2);
    });

    it('shows loading state for LOADING status', () => {
      renderMostValuable({ status: StatComponentStatus.LOADING });
      expect(screen.getAllByText('-')).toHaveLength(2);
    });
  });

  describe('when status is ERROR', () => {
    it('displays error screen', () => {
      renderMostValuable({ status: StatComponentStatus.ERROR });
      expect(document.querySelector('.statistics-non-plotly-error-container')).toBeInTheDocument();
    });
  });

  describe('with empty collection', () => {
    it('shows placeholder message', () => {
      const emptyCardGroup = create(FakeMTGCardGroup).set('cardInstances', Immutable.List<CardInstance>());
      renderMostValuable({ cardGroup: emptyCardGroup });

      expect(screen.getByText("Looks like your collection is empty, let's add some cards.")).toBeInTheDocument();
      expect(screen.getByText('Add Cards')).toBeInTheDocument();
    });
  });

  describe('with foil overlay', () => {
    it('shows foil overlay when card is foil', () => {
      const cardGroup = create(FakeMTGCardGroup);
      const foilCardInstance = cardGroup.get('cardInstances').first()!.set('foil', Foil.On);
      const updatedCardGroup = cardGroup.set('cardInstances', Immutable.List([foilCardInstance]));

      renderMostValuable({ cardGroup: updatedCardGroup });
      expect(document.querySelector('.foil-overlay')).toBeInTheDocument();
    });

    it('does not show foil overlay when card is not foil', () => {
      const cardGroup = create(FakeMTGCardGroup);
      const nonFoilCardInstance = cardGroup.get('cardInstances').first()!.set('foil', Foil.Off);
      const updatedCardGroup = cardGroup.set('cardInstances', Immutable.List([nonFoilCardInstance]));

      renderMostValuable({ cardGroup: updatedCardGroup });
      expect(document.querySelector('.foil-overlay')).not.toBeInTheDocument();
    });

    it('uses card back image when not loaded', () => {
      renderMostValuable({ status: StatComponentStatus.INACTIVE });
      const img = screen.getByRole('img');
      expect(img).toHaveAttribute('src', 'mock-card-back-url');
    });
  });

  describe('with react-waypoint', () => {
    test('loads statistics when status is INACTIVE', async () => {
      mockMostValuableCard.mockResolvedValue(undefined);
      const { dispatcher } = renderMostValuable({ status: StatComponentStatus.INACTIVE });

      await waitFor(() => {
        expect(mockChangeStatus).toHaveBeenCalledWith(
          dispatcher,
          StatComponentStatus.LOADING,
          StatisticType.MOST_VALUABLE,
        );
        expect(mockMostValuableCard).toHaveBeenCalledWith(dispatcher);
      });
    });

    test('loads statistics when status is ERROR', async () => {
      mockMostValuableCard.mockResolvedValue(undefined);
      const { dispatcher } = renderMostValuable({ status: StatComponentStatus.ERROR });

      await waitFor(() => {
        expect(mockChangeStatus).toHaveBeenCalledWith(
          dispatcher,
          StatComponentStatus.LOADING,
          StatisticType.MOST_VALUABLE,
        );
        expect(mockMostValuableCard).toHaveBeenCalledWith(dispatcher);
      });
    });

    test('handles loading error', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockMostValuableCard.mockRejectedValue(new Error('API Error'));

      const { dispatcher } = renderMostValuable({ status: StatComponentStatus.INACTIVE });

      await waitFor(() => {
        expect(mockChangeStatus).toHaveBeenCalledWith(
          dispatcher,
          StatComponentStatus.ERROR,
          StatisticType.MOST_VALUABLE,
        );
      });

      consoleSpy.mockRestore();
    });
  });

  describe('with refresh button', () => {
    test('triggers loadStatistics', async () => {
      mockMostValuableCard.mockResolvedValue(undefined);
      const { dispatcher } = renderMostValuable({ status: StatComponentStatus.LOADED });

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(mockChangeStatus).toHaveBeenCalledWith(
          dispatcher,
          StatComponentStatus.LOADING,
          StatisticType.MOST_VALUABLE,
        );
        expect(mockMostValuableCard).toHaveBeenCalledWith(dispatcher);
      });
    });

    test('handles refresh error', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockMostValuableCard.mockRejectedValue(new Error('API Error'));

      const { dispatcher } = renderMostValuable({ status: StatComponentStatus.LOADED });

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(mockChangeStatus).toHaveBeenCalledWith(
          dispatcher,
          StatComponentStatus.ERROR,
          StatisticType.MOST_VALUABLE,
        );
      });

      consoleSpy.mockRestore();
    });
  });
});

import { faker } from '@faker-js/faker';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { create, FakeTag, FakeUser } from '../../../tests/fake/Fake';
import { renderWithDispatcher } from '../../../tests/test-utils';
import * as FilterActions from '../../actions/FilterActions';
import displayCurrency from '../../helpers/currency_helper';
import history from '../../helpers/history';
import { TagStatisticsItem } from '../../models/TagStatisticsItem';
import { TagStatisticsCell } from './TagStatisticsCell';

vi.mock('../../actions/FilterActions', () => ({
  overrideWithTag: vi.fn(),
}));

vi.mock('../../helpers/history', () => ({
  default: {
    push: vi.fn(),
  },
}));

const mockOverrideWithTag = vi.mocked(FilterActions.overrideWithTag);
const mockHistoryPush = vi.mocked(history.push);

type TagStatisticsCellProps = ComponentProps<typeof TagStatisticsCell>;

const createFakeTagStatisticsItem = (options?: { totalValue?: number; totalCards?: number }) => {
  return new TagStatisticsItem({
    tag: create(FakeTag),
    totalCards: options?.totalCards ?? faker.number.int({ min: 1, max: 1000 }),
    totalValue: options?.totalValue ?? faker.number.float({ min: 0.01, max: 5000, multipleOf: 0.01 }),
  });
};

const mockTagRecord = createFakeTagStatisticsItem();

const DEFAULT_PROPS: Omit<TagStatisticsCellProps, 'dispatcher'> = {
  tagRecord: mockTagRecord,
  me: create(FakeUser, { username: faker.internet.userName() }),
};

const renderTagStatisticsCell = (props: Partial<Omit<TagStatisticsCellProps, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(TagStatisticsCell, { ...DEFAULT_PROPS, ...props });
};

describe('TagStatisticsCell', () => {
  it('displays tag name with hash prefix', () => {
    const tagRecord = createFakeTagStatisticsItem();
    const tagName = tagRecord.get('tag').get('name');

    renderTagStatisticsCell({ tagRecord });
    expect(screen.getByText(`#${tagName}`)).toBeInTheDocument();
  });

  it('displays total cards and total value', () => {
    renderTagStatisticsCell();
    expect(screen.getByText('Total Cards')).toBeInTheDocument();
    expect(screen.getByText('Total Value')).toBeInTheDocument();
  });

  describe('when totalValue is defined', () => {
    it('formats currency using displayCurrency helper', () => {
      const totalValue = faker.number.int({ min: 1, max: 10000 });
      const tagRecord = createFakeTagStatisticsItem({ totalValue });
      const user = create(FakeUser, { username: faker.internet.userName() });

      const { container } = renderTagStatisticsCell({ tagRecord, me: user });

      // The displayCurrency helper should format the value correctly
      const expectedCurrency = displayCurrency(
        totalValue,
        true,
        user.get('preferences').get('localization').get('currency'),
      );
      expect(container.textContent).toContain(expectedCurrency);
    });
  });

  describe('when totalValue is undefined', () => {
    it('displays dash for value', () => {
      const tagRecord = new TagStatisticsItem({
        tag: create(FakeTag),
        totalCards: faker.number.int({ min: 1, max: 100 }),
        totalValue: undefined,
      });

      renderTagStatisticsCell({ tagRecord });
      expect(screen.getByText('-')).toBeInTheDocument();
    });
  });

  describe('when disabled prop is false', () => {
    test('calls filterByTag function', async () => {
      mockOverrideWithTag.mockResolvedValue(undefined);
      const user = create(FakeUser, { username: faker.internet.userName() });

      const { dispatcher } = renderTagStatisticsCell({
        me: user,
        disabled: false,
      });

      const tagName = mockTagRecord.get('tag').get('name');
      const tagContainer = screen.getByText(`#${tagName}`).parentElement;
      fireEvent.click(tagContainer!);

      await waitFor(() => {
        expect(mockOverrideWithTag).toHaveBeenCalledWith(
          mockTagRecord.get('tag'),
          expect.objectContaining({
            ownerUsername: user.get('username'),
          }),
          dispatcher,
        );
        expect(mockHistoryPush).toHaveBeenCalledWith('/collection');
      });
    });
  });

  describe('when disabled prop is true', () => {
    test('does not call filterByTag on click', () => {
      renderTagStatisticsCell({ disabled: true });

      const tagName = mockTagRecord.get('tag').get('name');
      const tagContainer = screen.getByText(`#${tagName}`).parentElement;
      fireEvent.click(tagContainer!);

      expect(mockOverrideWithTag).not.toHaveBeenCalled();
      expect(mockHistoryPush).not.toHaveBeenCalled();
    });
  });
});

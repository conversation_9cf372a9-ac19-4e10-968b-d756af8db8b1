import { faker } from '@faker-js/faker';
import * as Immutable from 'immutable';
import { CartService, CartTableGrouping, ServiceType } from '../../src/lib/carts/CartService';
import { CartCard, CartCardRecord, CartData, ICartCard, ICartCardRecord, ICartData } from '../../src/models/CartCards';
import { create } from './Fake';
import { IFake } from './FakeInterface';

export class FakeCartCard implements IFake<CartCard> {
  fake(options?: ICartCard) {
    return new CartCard({
      jsonID: options?.jsonID || faker.string.uuid(),
      name: options?.name || faker.commerce.productName(),
      setName: options?.setName || faker.commerce.department(),
      setCode: options?.setCode || faker.string.alphanumeric(3).toUpperCase(),
      collectorNumber: options?.collectorNumber || faker.string.numeric(3),
    });
  }
}

export class FakeCartCardRecord implements IFake<CartCardRecord> {
  fake(options?: ICartCardRecord) {
    return new CartCardRecord({
      cartCard: options?.cartCard || create(FakeCartCard, options),
      quantity: options?.quantity ?? faker.number.int({ min: 1, max: 4 }),
      ownedCount: options?.ownedCount ?? faker.number.int({ min: 0, max: 10 }),
      deckCount: options?.deckCount ?? faker.number.int({ min: 0, max: 4 }),
    });
  }
}

export class FakeCartData implements IFake<CartData> {
  fake(options?: ICartData & { recordCount?: number }) {
    const recordCount = options?.recordCount ?? 3;
    const records = Array.from({ length: recordCount }, () => {
      const record = create(FakeCartCardRecord, options);
      return [record.get('cartCard').get('jsonID'), record] as [string, CartCardRecord];
    });

    return new CartData({
      data: Immutable.Map(records),
    });
  }
}

// Fake CartService implementations for testing

/**
 * Fake implementation of CardKingdom cart service for testing purposes.
 *
 * output:
 * - Partner codes and URLs
 * - payload format
 *
 * **Payload Pattern:** `quantity + space + card name + "||"` (repeated, ends with "||")
 *
 * @example
 * ```typescript
 * const service = new FakeCardKingdomService();
 * const payload = service.generatePayload(CartTableGrouping.NAME);
 * // "1 Lightning Bolt||2 Counterspell||3 Black Lotus||"
 * ```
 */
export class FakeCardKingdomService extends CartService {
  readonly type = ServiceType.CARDKINGDOM;
  readonly partnerCode = faker.internet.domainWord();
  readonly partnerURL = `https://${faker.internet.domainName()}/builder?partner=${this.partnerCode}&utm_source=${
    this.partnerCode
  }&utm_medium=affiliate&utm_campaign=${this.partnerCode}`;
  readonly iconURL = `/test-svg/${faker.system.fileName()}.svg`;
  readonly validGroupings = Immutable.Set([CartTableGrouping.NAME]);

  generatePayload(grouping: CartTableGrouping, cartData?: CartData): string {
    const items = Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => {
      const quantity = faker.number.int({ min: 1, max: 4 });
      const cardName = faker.commerce.productName();
      return `${quantity} ${cardName}`;
    });
    return items.join('||') + '||';
  }
}

/**
 * Fake implementation of TCGPlayer cart service for testing purposes.
 *
 * output:
 * - Partner codes and URLs
 * - payload format
 * - Multiple grouping options (Name, Set, Printing)
 *
 * **Payload Pattern:** `quantity + space + card name + space + [set code] + space + collector number + "||"` (repeated, ends with "||")
 *
 * @example
 * ```typescript
 * const service = new FakeTCGPlayerService();
 * const payload = service.generatePayload(CartTableGrouping.NAME);
 * // "1 Lightning Bolt [M25] 123||2 Counterspell [DOM] 456||3 Black Lotus [LEA] 789||"
 * ```
 */
export class FakeTCGPlayerService extends CartService {
  readonly type = ServiceType.TCGPLAYER;
  readonly partnerCode = faker.internet.domainWord();
  readonly partnerURL = `https://api.${faker.internet.domainName()}/massentry?partner=${this.partnerCode}&utm_source=${
    this.partnerCode
  }&utm_medium=affiliate&utm_campaign=${this.partnerCode}`;
  readonly iconURL = `/test-icons/${faker.system.fileName()}.png`;
  readonly validGroupings = Immutable.Set([CartTableGrouping.NAME, CartTableGrouping.SET, CartTableGrouping.PRINTING]);

  generatePayload(grouping: CartTableGrouping, cartData?: CartData): string {
    const items = Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => {
      const quantity = faker.number.int({ min: 1, max: 4 });
      const cardName = faker.commerce.productName();
      const setCode = faker.string.alphanumeric(3).toUpperCase();
      const collectorNumber = faker.string.numeric(3);
      return `${quantity} ${cardName} [${setCode}] ${collectorNumber}`;
    });
    return items.join('||') + '||';
  }
}
